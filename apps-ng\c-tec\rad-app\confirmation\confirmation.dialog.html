<rad-dialog-layout [title]="data.title" [severity]="data.severity" [header]="false" (confirm)="confirm()" (cancel)="cancel()">
    <div class="flex flex-col sm:flex-row flex-auto items-center sm:items-start p-8 pb-6 sm:pb-8">

        <!-- Icon -->
        <ng-container *ngIf="data?.showIcon">
            <div
                class="icon-halo mr-6" [ngClass]="contextColor">
                <i
                    class="icon icon-size-8"
                    [ngClass]="data?.icon"></i>
            </div>
        </ng-container>

        <ng-container *ngIf="data?.title || data?.message">
            <div class="flex flex-col items-center sm:items-start mt-4 sm:mt-0 sm:pr-8 space-y-1 text-center sm:text-left">

                <!-- Title -->
                <ng-container *ngIf="data?.title">
                    <div
                        class="text-lg leading-snug font-medium"
                        [innerHTML]="data?.title"></div>
                </ng-container>

                <!-- Message -->
                <ng-container *ngIf="data?.message">
                    <div
                        class="text-gray-600 text-sm"
                        [innerHTML]="data?.message"></div>
                </ng-container>
            </div>
        </ng-container>

    </div>
</rad-dialog-layout>

    


    

   

