import { Type, InjectionToken } from "@angular/core";
import { Data, DefaultExport } from "@angular/router";
import { IDialogContext } from "../dialog/dialog.types";



export interface AppViewData extends Data {
    isLayout?:boolean,
    icon?:string,
    title?:string,


}

export interface ViewInfo {
  name: string;
  data: AppViewData;

}


export interface AppView extends Data {
    /**
     * The name of the view being registered
     */
    name: string;

    /**
     * Object specifying a lazy loaded component
     */
    loadComponent: () =>  Promise<Type<unknown> | DefaultExport<Type<unknown>>>;

    data?: AppViewData,

    link?: {icon?:string, title:string};





}

export type RadViews = AppView[]


export const RAD_VIEWS = new InjectionToken<RadViews>("RAD_VIEWS");

export declare type ViewData = {
    [key: string]: any;
};





export interface InitView {
  init(context: any): Promise<any>;
}

export interface RadViewItem {

  id?: string;
  title?: string;
  subtitle?: string;
  hidden?: (item: RadViewItem) => boolean;
  disabled?: boolean;
  tooltip?: string;
  view?: string| Type<unknown>;

  classes?: {
    title?: string;
    subtitle?: string;
    icon?: string;
    wrapper?: string;
  };
  icon?: string;
  badge?: {
    title?: string;
    classes?: string;
  };

  meta?: any;
  viewId?: string;
  active?: boolean;
}


export enum ViewSize {

  Small = 'Small' as any,
  Medium = 'Medium' as any,
  Large = 'Large' as any,
  Full = 'Full' as any,
  Fit = 'Fit' as any
}

export enum DisplayMode {
    Default = 'Default' as any,
    Dialog = 'Dialog' as any,

}

export interface ViewConfiguration {
    size?: ViewSize,
    display?: DisplayMode
}

//export const VIEW_CONTEXT = new InjectionToken<ViewContext>("VIEW_CONTEXT")

export const VIEW_DATA = new InjectionToken<ViewData>("VIEW_DATA")




export interface IAppViewConfig {
  name?: string,
}

export class AppViewMeta {
  static readonly config = 'app-view-config'
  static getConfig(target: any): IAppViewConfig {

    const config =  Reflect.getMetadata(this.config, target)||{};

    if(!config.name){
      config.name = target.constructor.name;
    }

    return config;

  }
}

// Define a decorator to define the size of a dialog
export function AppViewConfig(config: IAppViewConfig) {
  return function (target: any) {

    // check for permission here


    Reflect.defineMetadata(AppViewMeta.config, config, target);


  };
}

export class ViewSizeConstants {
  public static readonly Small = '640px';
  public static readonly Medium = '840px';
  public static readonly Large = '1200px';

}

export type ViewDef<T = any> = string | Type<T>;