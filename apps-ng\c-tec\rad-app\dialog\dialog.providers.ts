import { EnvironmentProviders, Provider, importProvidersFrom } from "@angular/core";
import { AppDialogService } from "@tec/rad-core/abstractions";
import { RadAppDialogService } from "./dialog.service";
import {DEFAULT_DIALOG_CONFIG, DialogConfig, DialogModule} from "@angular/cdk/dialog";




const defaultDialogConfig: DialogConfig = {
    hasBackdrop: false,
    panelClass: 'rad-dialog-panel',
}

export function provideRadDialogService(): Array<Provider|EnvironmentProviders> {
    return [

      importProvidersFrom(DialogModule),
      {provide: AppDialogService, useExisting: RadAppDialogService },
      {provide: DEFAULT_DIALOG_CONFIG, useValue: defaultDialogConfig}
    ];
  }
