import { AsyncPipe, CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, Type, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormlyFieldConfig,  FieldType, FieldTypeConfig, FormlyModule } from '@ngx-formly/core';
import { FormlyFieldSelectProps } from '@ngx-formly/core/select';
import { ListboxModule, Listbox } from 'primeng/listbox';
import { ButtonModule } from 'primeng/button';
import { FormlyFormFieldModule } from '@ngx-formly/primeng/form-field';
import { FormlySelectModule } from '@ngx-formly/core/select';
import { BehaviorSubject, Observable } from 'rxjs';
import { RadFormFieldProps } from '../../form-types';
import { toSignal } from '@angular/core/rxjs-interop';
import { RadSelectOptionsPipe } from './select-pipe';

type OptionsType = any[] | Observable<any[]>;

interface ListboxProps extends RadFormFieldProps, FormlyFieldSelectProps {
  add?: (field: FormlyFieldConfig<ListboxProps>) => any;
  addIcon?: string;
  addLabel?: string;
  showClear?: boolean;
  scrollHeight?: string;
  group?: boolean;
  optionGroupChildren?: string;
}

export interface FormlyListboxFieldConfig extends FormlyFieldConfig<ListboxProps> {
  type: 'listbox' | Type<RadFormlyListbox>;
}

@Component({
  selector: 'rad-formly-listbox',
  template: `
    <p-listbox
    [options]="props.options | radSelectOptions : field | async"
      
      [formControl]="formControl"
      [formlyAttributes]="field"
      [multiple]="true"
      [checkbox]="true"
      [selectAll]="false"
      [showToggleAll]="false"
      [autoOptionFocus]="false"
      [scrollHeight]="props.scrollHeight || 'auto'"
      [group]="props.groupProp || false"
      [optionGroupChildren]="'group'" 
      class="rd-listbox-flex"
      (onChange)="props.change && props.change(field, $event)"
    >
      <!-- <ng-template let-group #group *ngIf="props.group">
        <div class="flex items-center border-b py-1 border-b border-b-slate-100">
          <span>{{ group.label }}</span>
        </div>
      </ng-template> -->
    </p-listbox>
    
    <!-- Optional footer button -->
    <div *ngIf="props.add" class="w-full flex flex-row px-2 py-2 justify-end bg-gray-50 border-t-gray-100 rounded-br-md rounded-bl-md mt-2">
      <button type="button"
              pButton 
              [icon]="props.addIcon || 'pi pi-plus'"
              [label]="props.addLabel || 'Add'"
              (click)="addOption()">
      </button>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ListboxModule,
    FormlyFormFieldModule,
    FormlySelectModule,
    RadSelectOptionsPipe,
    FormlyModule,
    ButtonModule,
    AsyncPipe
  ]
})
export class RadFormlyListbox extends FieldType<FieldTypeConfig<ListboxProps>> {
  private control = viewChild(Listbox);




  async addOption() {
    if (!this.props.add) {
      return;
    }
    this.props.add(this.field);
  }

 get optionItems() {
    const options = this.props.options;
    if (!(options instanceof Observable)) {
      return new BehaviorSubject(options);
    }
    return options;

    


  }

  
}