import { ComponentType } from "@angular/cdk/portal";
import { inject, Injectable, TemplateRef } from "@angular/core";
import { DialogOptions,  DialogResult, AppDialogService, DialogMetadata, AppViewResolver } from "@tec/rad-core/abstractions";
import { firstValueFrom } from "rxjs";
import { CdkDialogManager } from "./dialog.manager";
import "reflect-metadata";
@Injectable({providedIn: 'root'})
export class RadAppDialogService extends AppDialogService {



    private dialogManager = inject(CdkDialogManager);
    private viewManager = inject(AppViewResolver, {optional: true});

    constructor(){
        super()
    }


    private getDefaultOptions(): DialogOptions {
        return {
            size: 'medium',
            placement: "center"
        }
    }


    async show<R = any, T = unknown>(componentRef: ComponentType<T> | TemplateRef<T>, data?: any, options?: DialogOptions): Promise<DialogResult<R>> {

        const metaConfig = DialogMetadata.getConfig(componentRef);
        

        
        
        const dialogOptions = {...this.getDefaultOptions(), ...metaConfig, ...options};

        if(dialogOptions.placement == "side") {
            const result = await firstValueFrom(this.dialogManager.showAside(componentRef,data, options))
            return result;
        }else {
            const result = await this.dialogManager.showAsync(componentRef, data, options);
            return result;
        }

    }

    async showAside<R = any, T = unknown>(componentRef: ComponentType<T> | TemplateRef<T>, data?: any, options?: DialogOptions): Promise<DialogResult<R>> {
        const result = await firstValueFrom(this.dialogManager.showAside(componentRef,data, options))
        return result;
    }

    async showViewAside<R = any>(viewName: any, data?: any, options?:DialogOptions): Promise<DialogResult<R>> {
        if(!this.viewManager){
            return DialogResult.cancel();
        }
        const view = await this.viewManager.getView(viewName);

        if(!view){
            return DialogResult.cancel();
        }

        const result = await this.showAside(view, data, options);
        return result;

    }












}
