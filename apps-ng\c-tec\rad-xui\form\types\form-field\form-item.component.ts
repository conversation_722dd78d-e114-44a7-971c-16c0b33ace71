import { Component } from '@angular/core';
import { <PERSON><PERSON>rap<PERSON>, FormlyFieldConfig } from '@ngx-formly/core';
import { RadFormFieldProps } from '../../form-types';
import { RadFormFieldEdit } from './form-item-edit.component';
import { RadFormFieldInfo } from './form-item-info.component';

@Component({
  selector: 'rad-formly-field',
  template: `
    @if (infoMode) {
      <rad-form-field-info [field]="field"></rad-form-field-info>
    } @else {
      <rad-form-field-edit [field]="field">
<ng-container #fieldComponent></ng-container>      
      </rad-form-field-edit>
    }
  `,
  imports: [RadFormFieldEdit, RadFormFieldInfo]
})
export class RadFormItemComponent extends FieldWrapper<FormlyFieldConfig<RadFormFieldProps>> {
  get infoMode() {
    return this.props.infoMode ?? this.options.formState?.infoMode;
  }
}