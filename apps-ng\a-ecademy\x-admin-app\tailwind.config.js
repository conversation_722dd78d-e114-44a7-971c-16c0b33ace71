const { createGlobPatternsForDependencies } = require('@nx/angular/tailwind');
const { join } = require('path');
//const sharedTailwindConfig = require('../../c-tec/@rad-ui/resources/modern/modern.preset');
const sharedTailwindConfig = require('../../c-tec/rad-ui/theme/evo/tailwind.preset');
//const sharedTailwindConfig = require('../../c-tec/@rad-ui/x-tailwind/tailwick.preset');
module.exports = {
  presets: [sharedTailwindConfig],
  content: [
    'a-ecademy/x-admin-app/**/*.{html,scss,ts}',
    'c-tec/rad-ui/**/*.{html,scss,ts}',
    'c-tec/rad-app/**/*.{html,scss,ts}',
    'c-tec/rad-xui/**/*.{html,scss,ts}',
  ],
};

