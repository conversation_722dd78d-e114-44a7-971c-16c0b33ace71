{"name": "ed-admin-courses", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/admin/courses/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/admin/courses/ng-package.json", "tsConfig": "a-ecademy/admin/courses/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/admin/courses/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "lint": {"executor": "@nx/eslint:lint"}}}