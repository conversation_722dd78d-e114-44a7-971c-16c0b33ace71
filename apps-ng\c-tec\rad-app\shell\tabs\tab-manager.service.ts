import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';



import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { TabConfig } from './tab-config';
import { TabItem } from './tab-item';
import { KeyedCollection, UrlHelper } from '@tec/rad-core/utils';
import { AppMediator, AppTabManager, ITabItem, PageInfo } from '@tec/rad-core/abstractions';
import { RouteActivatedEvent, RouteDeactivatedEvent } from '@tec/rad-core/composition';




@Injectable({
    providedIn: 'root'
})

export class TabManagerService extends AppTabManager {

    #mediator = inject(AppMediator);

    private _tabs$: BehaviorSubject<TabItem[]>

    private _tabTitles = new KeyedCollection<PageInfo>();
    
    private _counter = 1;
    public get tabs$() {
        return this._tabs$.asObservable();
    }

    public get tabs() {
        return this._tabs$.value;
    }

    private _tabClosed$: Subject<TabItem> = new Subject<TabItem>();

    get tabClosed$(): Observable<TabItem>{
        return this._tabClosed$.asObservable();
    }

    private _tabSelected$: Subject<TabItem> = new Subject<TabItem>();

    get tabSelected$(): Observable<TabItem>{
        return this._tabSelected$.asObservable();
    }

    private _tabUrlChanging$: Subject<TabItem> = new Subject<TabItem>();

    get tabUrlChanging$(): Observable<TabItem>{
        return this._tabUrlChanging$.asObservable();
    }

    private _defaultTab: TabItem;
    private _activeTab: TabItem;


    constructor(
        //private shell: AppNavigationService
        ) {
        super();
        this._defaultTab = new TabItem();
        this._defaultTab.isDefault = true;
        this._defaultTab.setTitle('Home');



        this._tabs$ = new BehaviorSubject<TabItem[]>([this._defaultTab]);

        this.activateTab(this._defaultTab);


    }


    

   

    

    private updateTabs(tabs: TabItem[]) {
        this._tabs$.next(tabs);
        
    }

    activateTabForUrl(url: string) {

        const tab = this.findTabOrDefault(url);
        if(tab.currentUrl != url){
            this._tabUrlChanging$.next(tab);
            tab.updateCurrentUrl(url);
        }

        if(tab.isDefault){
           const info = this._tabTitles[url];
           if(info) {
               tab.title = info.title;
               tab.subtitle = info.subTitle;
           }
           this.updateTabs(this.tabs);
        }
        
        this.activateTab(tab);
        return;
        

        

    }


    

    registerTab(config: TabConfig): ITabItem {


        const existing = this.findTabRootForUrl(UrlHelper.normalize(config.currentUrl));
        console.log(`Looking for tab for ${config.currentUrl}`)
        if (existing) {
            //if(existing.currentUrl == existing.rootUrl) {
                existing.updateUrlConfig(config);
            //}
            return existing;
        }

        const item = this.createNewTab();
        item.rootUrl = config.rootUrl;
        item.updateUrlConfig(config);
        item.title = config.title;
        item.subtitle = config.subtitle;
        item.path = config.path;
        item.pathKey = config.pathKey;

        this.addTab(item);
        return item;

    }


    private createNewTab(): TabItem {

        const tab = new TabItem();
        tab.id = this.createId();
        tab.subscription = tab.titleChanged$.subscribe(n=>this.updateTabs(this.tabs))
        return tab;
    }

    private createId(): string {
        this._counter ++;
        return this._counter.toString();
    }

    addTab(item: TabItem) {

        const tabs = [...this._tabs$.value, item];
        this.updateTabs(tabs);

    }


    findTabOrDefault(url: string): TabItem | undefined {


        const exactMatch = this.tabs.find(n => !n.isDefault && n.currentUrl == UrlHelper.normalize(url));

        if (exactMatch) {
            return exactMatch;
        }

        for(const tab of this.tabs) {
            if(tab.isRootOf(url)) {
                return tab;
            }
        }

        

        return this._defaultTab;


    }

    

    findTabWithUrl(url: string): TabItem {
        //return this.findTabRootForUrl(url);
        for(const tab of this.tabs) {
            if(tab.currentUrl.toLowerCase() === UrlHelper.normalize(url).toLowerCase()) {
                return tab;
            }
        }
        return undefined;
    }

    findTabRootForUrl(url: string): TabItem | undefined {


        for(const tab of this.tabs) {
            if(tab.isRootOf(UrlHelper.normalize(url))) {
                return tab;
            }
        }
        return undefined;


    }

    selectTab(tab: TabItem): void {
        this._tabSelected$.next(tab);
    }

    private activateTab(tab: TabItem): void {

        this._activeTab = tab;
        const tabs = this.tabs;

        tabs.forEach(n => {
            if(n.isActive && n.currentUrl != tab.currentUrl) {
                this.deactivateTab(n);
            }
            
           
        });

        this.#mediator.publish(new RouteActivatedEvent(tab.rootUrl));

        if(tab.isDefault) {
            if(this._tabTitles.containsKey(tab.currentUrl)){
                const info = this._tabTitles.get(tab.currentUrl);
                tab.title = info.title;
                tab.subtitle = info.subTitle;

                const tabs = this.tabs;
                this.updateTabs(tabs);
            }
        }

        tab.activate(true);

        this.updateTabs(tabs);




    }

    private deactivateTab(tab: TabItem): void {
        tab.activate(false);
        this.#mediator.publish(new RouteDeactivatedEvent(tab.rootUrl));
        //this.#routeTransition.removeOutletByPath(tab.currentUrl);
    }

    closeTab(tab: TabItem): void {

        if(tab.isDefault) {
            return;
        }

        const tabs = this.tabs;


        const tabIndex = tabs.findIndex(t => t.currentUrl === tab.currentUrl);




        

        if (tabIndex === 0) {
            return;
        }

        this._tabClosed$.next(tab);
        tab.close();
        tab.subscription.unsubscribe();

        const previousTab = tabIndex > 0 ? tabs[tabIndex - 1] : this._defaultTab;

        const newTabs = tabs.filter(n=>n.currentUrl != tab.currentUrl);

        this.updateTabs(newTabs);

        
        this.selectTab(previousTab);


    }

    getTabReference(route: ActivatedRouteSnapshot): ITabItem {
        const url = UrlHelper.getResolvedUrl(route);
        const tab = this.findTabRootForUrl(url);
        return tab;
    }

    setTabTitle(route: ActivatedRouteSnapshot, title: string, subtitle?:string): TabItem {
        const info = new PageInfo();
        info.url = UrlHelper.getResolvedUrl(route);
        info.title = title;
        info.subTitle = subtitle;
    
        return this.updateTab(info);
        
    
      }

      setDefaultTitle(route: ActivatedRouteSnapshot, title: string, subtitle?:string): TabItem {
        const info = new PageInfo();
        info.url = UrlHelper.getResolvedUrl(route);
        info.title = title;
        info.subTitle = subtitle;
    
        return this.updateTab(info);
        
    
      }

      


      private updateTab(info: PageInfo): TabItem {

        const tab = this.findTabOrDefault(info.url);

        if(tab === undefined) {

            this._tabTitles.add(info.url, info);
            return undefined;
        }

        tab.title = info.title;
        tab.subtitle = info.subTitle;

        const tabs = this.tabs;
        this.updateTabs(tabs);
        return tab;

    }



    


}
