import { CommonModule } from '@angular/common';
import { Component, computed, inject, input } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormlyFormOptions, FormlyModule } from '@ngx-formly/core';
import { FieldLayout, FieldMode, RadFormConfig } from '../form-types';
import { FormBuilder } from '../form-builder';
import { RadDataForm } from '../data-form';

@Component({
    selector: 'rad-form',
    template: `
    <div class="rad-form" ngClass="{'read-only': readOnly}">
        <formly-form [form]="form()" [model]="model()" [fields]="fields()" [options]="options"  />
    </div>
    `,
    styles: [],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        FormlyModule
    ],

})
export class RadForm2 {


    #builder = inject(FormBuilder);
    form = input<FormGroup | RadDataForm | undefined>(undefined)

    layout = input<FieldLayout>('vertical');
    editMode = input<FieldMode>('edit');
    labelWidth = input<string|undefined>('120px');

    model = input<any>(undefined);

    options = input<FormlyFormOptions>()

    
    items = input<RadFormConfig>([]);

    private _fields = computed(() => {
        const items = this.items();
        const mode = this.editMode();
        const layout = this.layout();
        const labelWidth = this.labelWidth();

        if(!items || items.length === 0) {
            return [];
        }

        return items?.map((field) => {
            field.type = field.type ?? 'input';
            field.props = field.props ?? {};
            field.props.layout = layout;
            field.props.infoMode = mode === 'info';
            field.props.labelWidth = field.props.lableWidth ?? labelWidth ;
            return field;
        });
    });

    protected fields = computed(() => {
        const fields = this.#builder.createFields(this._fields());
        return fields;
    });


    validate(){
        //this.form().markAllAsTouched();
        //return !this.form().invalid;
    }

}