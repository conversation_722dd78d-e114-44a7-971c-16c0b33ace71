import { ModuleInfo } from '@tec/rad-core/abstractions';

export const CourseRunsModule: ModuleInfo = {
    name: 'CourseDeliveryModule',
    path: 'course-run',
    routes: [
        {
            name: 'CourseRun', path: ':courseRunId', data: {tabPage:true, title:'Course Run'},
            loadComponent: () => import('./feature-course-run').then(m => m.CourseRunPage),
        },
    ],
    views: [
        {name: 'CourseRuns', loadComponent: () => import('./feature-course-runs').then(m => m.CourseRunsView)},
        {name: 'CourseRunDetail', loadComponent: () => import('./feature-course-run').then(m => m.RunDetailView)}
    ]
};
