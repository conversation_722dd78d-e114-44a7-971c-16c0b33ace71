import { Injectable } from '@angular/core';
import { NavigationEnd, ResolveEnd, Router } from '@angular/router';


import { filter } from 'rxjs/operators';

import { TabItem } from './tab-item';
import { TabManagerService } from './tab-manager.service';
import { UrlHelper } from '@tec/rad-core/utils';


@Injectable({
    providedIn: 'root'
})

export class TabRouteWatcherService {

    


    constructor(private router: Router,
        private tabManager: TabManagerService
        
        ) {

        


        

        this.router.events.pipe(
            filter(event => event instanceof ResolveEnd)
        ).subscribe((navEnd) => {
            this.onResolvenEnd(navEnd as ResolveEnd);
        });

        this.router.events.subscribe(event=>{
            if(event instanceof NavigationEnd) {
           
                this.onNavEnd(event)
            }
        });

        // this.router.events.pipe(
        //     filter(event => event instanceof NavigationEnd)
        // ).subscribe((navEnd) => {
        //     this.onNavEnd(navEnd as NavigationEnd);
        // });

        tabManager.tabSelected$.subscribe(tab=>this.goToTab(tab));

        
    }


    

    init() {

    }

    
    private onResolvenEnd(event: ResolveEnd) {

        
        this.tabManager.activateTabForUrl(event.urlAfterRedirects);
        
       
        

        

    }


    private onNavEnd(event: NavigationEnd) {

        const tab = this.tabManager.findTabOrDefault(event.urlAfterRedirects);
        if(tab) {
            tab.updateLinkUrl(event.urlAfterRedirects);
        };
        //const params = UrlHelper.getQueryParamsFromUrl(event.urlAfterRedirects);


        //this.tabManager.activateTabForUrl(event.urlAfterRedirects);
        
       
        

        

    }

    private goToTab(tab: TabItem): void {
        console.log(`Going to tab with URL ${tab.currentUrl} link ${tab.linkUrl}`)
        if(tab.isActive){
            return;
        }
        // let urlWithParams = tab.currentUrl;
        // if(tab.queryParams) {
        //     urlWithParams = tab.currentUrl + '?' + this.serializeQueryParams(tab.queryParams);
        // }
        

        if(tab.linkUrl) {
            this.router.navigateByUrl(tab.linkUrl);
        }else{
            this.router.navigateByUrl(tab.currentUrl);
        }

        
    }


    // Helper function to serialize query parameters
serializeQueryParams(params: any): string {
    return Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  }
    


    


}
