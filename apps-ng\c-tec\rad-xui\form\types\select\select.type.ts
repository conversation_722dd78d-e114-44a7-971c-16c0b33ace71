import { Component, ChangeDetectionStrategy, Type, viewChild } from '@angular/core';
import { FieldType, FieldTypeConfig, FormlyFieldConfig, FormlyModule } from '@ngx-formly/core';
import { FormlyFormFieldModule } from '@ngx-formly/primeng/form-field';
import { FormlyFieldSelectProps } from '@ngx-formly/core/select';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FluidModule } from 'primeng/fluid';
import { Select, SelectModule } from 'primeng/select';
import { FormlySelectModule as FormlyCoreSelectModule } from '@ngx-formly/core/select';
import { ButtonModule } from 'primeng/button';
import { Observable } from 'rxjs';
import { RadFormFieldProps } from '../../form-types';


type OptionsType = any[] | Observable<any[]>;

interface SelectProps extends RadFormFieldProps, FormlyFieldSelectProps {
  appendTo?: any;
  filter?: boolean;
  add?: (field: FormlyFieldConfig<SelectProps>)=>any;
  addIcon?: string;
  addLabel?: string;
  showClear?: boolean;
}

export interface FormlySelectFieldConfig extends FormlyFieldConfig<SelectProps> {
  type: 'select' | Type<RadFormlySelect>;
}

@Component({
    selector: 'rad-formly-select',
    template: `
    <p-select
      [placeholder]="props.placeholder"
      [options]="props.options | formlySelectOptions : field | async"
      [formControl]="formControl"
      [formlyAttributes]="field"
      [showClear]="props.showClear"
      [appendTo]="props.appendTo"
      [filter]="props.filter"
      (onChange)="props.change && props.change(field, $event)"
      appendTo="body" fluid
    >
    <!-- Optional footer button -->
    <ng-template pTemplate="footer" *ngIf="props.add">
        <div class="w-full flex flex-row px-2 py-2 justify-end bg-gray-50 border-t-gray-100 rounded-br-md rounded-bl-mds">
          <button type="button"
                  pButton 
                  [icon]="props.addIcon || 'pi pi-plus'"
                  [label]="props.addLabel || 'Add'"
                  (click)="addOption()">
          </button>
        </div>
      </ng-template>
    </p-select>
  `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports:[
      CommonModule,
          ReactiveFormsModule,
          SelectModule,
          FluidModule,
          FormlyFormFieldModule,
          FormlyCoreSelectModule,
          FormlyModule,
          ButtonModule
    ]
    
})
export class RadFormlySelect extends FieldType<FieldTypeConfig<SelectProps>> {

  private control = viewChild(Select)

  async addOption() {

    if(!this.props.add) {
      return;
    }
    this.control().hide();
    this.props.add(this.field);
    
    
    
  }

  

  

}
