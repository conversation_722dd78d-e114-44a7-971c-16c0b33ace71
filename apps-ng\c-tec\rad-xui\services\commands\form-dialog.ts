import { CommonModule } from '@angular/common';
import { Component, signal, OnInit, viewChild } from '@angular/core';
import { RadForm, RadFormConfig, RadFormLayout } from '@tec/rad-xui/form';
import { RadDialogLayout } from '@tec/rad-ui/layout';


import { injectDialogInstance } from '@tec/rad-core/abstractions';


export interface FormDialogData {
    title: string;
    fields: RadFormConfig;
    data: any;

}

@Component({
    selector: 'rad-form-dialog',
    template: `
    <rad-dialog-layout [title]="title()" (confirm)="submit()" (cancel)="cancel()">
        <rad-form-layout #form [model]="data()" [items]="fields()" />
    </rad-dialog-layout>
    `,
    styles: [],
    imports: [
        CommonModule,
        RadFormLayout,
        RadDialogLayout

    ],

})
export class FormDialog implements OnInit {
    

    #dialogInstance = injectDialogInstance<FormDialogData>();
    #dialogData = this.#dialogInstance.data;
    private form = viewChild(RadForm);

    protected data = signal<any>({});
    protected fields = signal<RadFormConfig>([]);
    protected title = signal<string>('');

    ngOnInit(): void {
        this.data.set(this.#dialogData.data);
        this.fields.set(this.#dialogData.fields);
        this.title.set(this.#dialogData.title);
    }


    cancel() {
        this.#dialogInstance.cancel();
    }


    submit(){
        
        if(this.form().validate()){
            this.#dialogInstance.confirm(this.data());
        }

    }

}