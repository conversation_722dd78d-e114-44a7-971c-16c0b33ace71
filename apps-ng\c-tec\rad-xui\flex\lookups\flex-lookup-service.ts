import { inject, Injectable } from "@angular/core";
import { apiResultFrom, KeyedCollection, SelectItem } from "@tec/rad-core/utils";
import { LookupApiProxy } from "@tec/rad-xui/data";
import { from, Observable, of } from "rxjs";



type LookupGroup = {
    group: string;
    items: SelectItem[];
}

@Injectable({providedIn: 'root'})
export class RadLookupService {


    #lookupApi = inject(LookupApiProxy);

    private _lookups = new KeyedCollection<LookupGroup>();

    async getLookups(group: string): Promise<SelectItem[]> {

        if(this._lookups.containsKey(group)){
            return this._lookups.get(group).items;
        }

        const result = await apiResultFrom(this.#lookupApi.getLookups(group));

        if(result.isError){
            return [];
        }

        const items = result.value.items.map(n => new SelectItem(n.value, n.label));
        this._lookups.add(group, {group, items});
        return items;

    }

    getLookup$(group: string): Observable<SelectItem[]> {

        return from(this.getLookups(group));
    }




}