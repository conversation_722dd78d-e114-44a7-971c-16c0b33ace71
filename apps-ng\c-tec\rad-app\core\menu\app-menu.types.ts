import { InjectionToken, Provider, Signal } from "@angular/core";
import { INavItem } from "@tec/rad-core/abstractions";

import { Observable } from "rxjs";

export const I_APP_MENU_SERVICE = new InjectionToken<IAppMenuService>('IAppMenuService');

export interface IAppMenuService {

    readonly  menuItems: Signal<INavItem[]>
    loadMenu(items: INavItem[]): void

}

export abstract class AppMenuService implements IAppMenuService {

    abstract menuItems: Signal<INavItem[]>
    abstract loadMenu(items: INavItem[]): void

}

export const APP_MENU_ITEMS = new InjectionToken<INavItem[]>('APP_MENU_ITEMS');

export function provideAppMenu(menuItems: INavItem[]): Provider {
    return {
         provide: APP_MENU_ITEMS, 
         useFactory: () => menuItems
         }
    
}