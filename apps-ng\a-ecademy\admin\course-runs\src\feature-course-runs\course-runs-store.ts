import { computed, inject, Injectable, resource } from "@angular/core";
import { CourseRunApiProxy, CreateCourseRunCommand } from "@ed/shared/data-academics";
import { apiResultFrom, executeApi, injectRouteParam } from "@tec/rad-core/utils";
import { RadFormConfig } from "@tec/rad-xui/form";
import { CommandService, RadSignalStore } from "@tec/rad-xui/services";

@Injectable()
export class CourseRunsFacade extends RadSignalStore {

    #runsApi = inject(CourseRunApiProxy);
    #commands = inject(CommandService);
    private courseId = injectRouteParam("courseId");
    

    constructor() {
        super();
    }

    private _runs = resource({
        params: ()=> this.courseId,
        loader: (params) =>{
            return executeApi(this.#runsApi.getCourseRuns(params.params))
        } 
    })  

    runs = computed(() => this._runs.value()?.items);
    loading = computed(() => this._runs.isLoading());



    private creatteRunFields: RadFormConfig = [
        { key: 'name', label: 'Name', type: 'input', rowNum: 1, required: true},
        { key: 'description', label: 'Description', type: 'textarea', rowNum: 2},
        { key: 'startDate', label: 'Start Date', type: 'datepicker', rowNum: 3, required: true},
        { key: 'endDate', label: 'End Date', type: 'datepicker', rowNum: 3, required: true},
    ]


    async createRun(){
        const cmd = new CreateCourseRunCommand();
        cmd.courseId = this.courseId;
        await this.#commands.openForm(cmd,  this.creatteRunFields, "Create Course Run",

            async (data)=> {
                const result =  await apiResultFrom(this.#runsApi.createCourseRun(data));
                if(result.isSuccess) {
                    this._runs.reload();
                    return true;
                }
                return false;
                
            }
        );
        this._runs.reload();
    }

}