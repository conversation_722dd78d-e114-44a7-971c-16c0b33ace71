import { IsActiveMatchOptions, Params, QueryParamsHandling } from '@angular/router';

export interface AppPath {
    name: string;

    path: string;

    id?: string;
}

export type NavIconType = 'font' | 'svg' | 'dot';

export interface INavItem
{



    id?: string;
    title?: string;
    subtitle?: string;
    type?:
        | 'aside'
        | 'basic'
        | 'collapsable'
        | 'divider'
        | 'group'
        | 'spacer';
    hidden?: (item: INavItem) => boolean;
    active?: boolean;
    disabled?: boolean;
    tooltip?: string;
    link?: string;
    externalLink?: boolean;
    fragment?: string;
    preserveFragment?: boolean;
    queryParams?: Params | null;
    queryParamsHandling?: QueryParamsHandling | null;
    target?:
        | '_blank'
        | '_self'
        | '_parent'
        | '_top'
        | string;
    exactMatch?: boolean;
    isActiveMatchOptions?: IsActiveMatchOptions;
    function?: (item: INavItem) => void;
    classes?: {
        title?: string;
        subtitle?: string;
        icon?: string;
        wrapper?: string;
    };
    icon?: string;
    iconType?:NavIconType;
    badge?: {
        title?: string;
        classes?: string;
    };
    children?: INavItem[];
    meta?: any;
    page?: string;

    
    
}

export class NavigationItem implements INavItem
{



    id?: string;
    title?: string;
    subtitle?: string;
    type?:
        | 'aside'
        | 'basic'
        | 'collapsable'
        | 'divider'
        | 'group'
        | 'spacer' = 'basic';
    hidden?: (item: NavigationItem) => boolean;
    active?: boolean;
    disabled?: boolean;
    tooltip?: string;
    link?: string;
    externalLink?: boolean;
    target?:
        | '_blank'
        | '_self'
        | '_parent'
        | '_top'
        | string;
    exactMatch?: boolean;
    isActiveMatchOptions?: IsActiveMatchOptions;
    function?: (item: NavigationItem) => void;
    classes?: {
        title?: string;
        subtitle?: string;
        icon?: string;
        wrapper?: string;
    };
    icon?: string;
    iconType?: NavIconType;
    badge?: {
        title?: string;
        classes?: string;
    };
    children?: NavigationItem[];
    meta?: any;
    page?: string;

    constructor(title?: string, icon?: string, route?: string) {
        this.link = route;
        this.title = title;
        this.icon = icon;
      }
    
}

