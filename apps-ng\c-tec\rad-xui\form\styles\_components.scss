:root {
  --rd-list-item-size: 160px;
}

$heightMd: 32px;
.rad-field {
  @apply text-sm;
  display: flex;
  flex-direction: row;
  margin-bottom: 12px;

  .content-wrapper {
    @apply flex flex-col flex-auto;
    width: 100%;
  }

  &.rad-field-edit {
    margin-bottom: 12px;
    &.rad-field-horizontal {
      flex-direction: row;
      width: 100%;
      align-items: flex-start;
      margin-bottom: 8px;
      .label-wrapper {
        @apply flex-none;
        min-width: 40px;
        max-width: 280px;
        margin-bottom: 0;
        align-items: center;
      }

      .component-wrapper {
        @apply flex-auto;
        width: 100%;
      }
      .content-wrapper {
        padding-left: 16px;
      }
      .error-wrapper {
        align-self: flex-end;
        margin-left: 8px;
      }
    }

    &.rad-field-vertical {
      display: flex;
      flex-direction: column;

      .label-wrapper {
        height: 16px;
        max-height: 16px;
        min-height: 16px;
        margin-bottom: 8px;
      }
      .component-wrapper {
        width: 100%;
        box-sizing: border-box;
      }
      .error-wrapper {
        min-height: 12px;
        height: 12px;
      }
    }
  }

  &.rad-field-info {
    .info-icon {
      @apply flex-none flex flex-col justify-center items-center p-2 bg-slate-100 rounded-md;
      width: 28px;
      height: 28px;
      margin-right: 8px;
      i {
        @apply text-gray-500 icon-size-4;
      }
    }
    &.rad-field-horizontal {
      margin-bottom: 8px;
      flex-direction: row;
      width: 100%;
      align-items: flex-start;
      .content-wrapper {
        flex-direction: row;
        width: 100%;
        align-items: flex-start;
        .label-wrapper {
          @apply flex-none;
          min-width: 40px;
          max-width: 280px;
          margin-bottom: 0;
          align-items: center;
        }

        .component-wrapper {
          @apply flex-auto;
          width: 100%;
        }
        .content-wrapper {
          padding-left: 16px;
        }
        .error-wrapper {
          align-self: flex-end;
          margin-left: 8px;
        }
      }
    }

    &.rad-field-vertical {
      .content-wrapper {
        display: flex;
        flex-direction: column;
        .label-wrapper {
          height: 16px;
          max-height: 16px;
          min-height: 16px;
          margin-bottom: 8px;
        }
        .component-wrapper {
          width: 100%;
          box-sizing: border-box;
        }
        .error-wrapper {
          min-height: 12px;
          height: 12px;
        }
      }
    }

    .field-required {
      display: none;
    }
  }

  .formly-info-value {
    @apply text-gray-900 text-sm;

    min-height: 12px;
    padding: 0px 0;

    display: flex;
    align-items: center;
  }

  &.rad-field-hilite {
  }
}

rad-formly-input2 {
  //input
  input.p-inputtext {
    @apply control-height;
    width: 100%;
    padding-top: 8px;
    padding-bottom: 8px;
    //height: var(--rd-control-height);
  }
}

rad-formly-select2 {
  .p-select {
    width: 100%;

    .p-inputwrapper {
      @apply control-height;
    }

    .p-inputtext {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }
}

rad-formly-datepicker2 {
  span.p-datepicker {
    @apply control-height;
    width: 100%;

    input {
      height: 100%;
    }

    button {
      height: 100%;
    }
  }

  .p-calendar {
    .p-datepicker {
      @apply text-sm;
      min-width: 1%;

      td {
        padding: 4px;
      }
    }
  }
}

rad-formly-checkbox {
  .p-checkbox {
    @apply text-base leading-none #{!important};
    padding-top: 0;
    padding-bottom: 0;
  }
}

rad-formly-textarea2 {
  textarea.p-inputtext {
    width: 100%;
  }
}

rad-formly-radio2 {
  .radio-wrapper {
    @apply flex flex-row align-middle rounded-md border border-slate-300 px-2 gap-2;
    min-height: $heightMd;
  }
}

body > .p-datepicker-panel {
  position: absolute !important;
  z-index: 9999;
}

.flat-listbox .p-listbox {
  box-shadow: none;
  border: none !important;
  --p-list-option-group-padding: 0px !important;
  .p-listbox-option-group {
    padding: 2px 0px 6px !important;
  }
}

.rd-listbox-flex {
  background-color: red;
  .p-listbox {
    width: 100%;
    .p-listbox-list {
      flex-direction: row;
      flex-wrap: wrap;
      padding: 8px;
      column-gap: 16px;
      row-gap: 4px;

      .p-listbox-option-group {
        width: 100%;
        padding: 4px 0px;
        border-radius: 4px;
        border-bottom: 1px solid var(--tw-gray-200);
        margin-bottom: 4px;
      }

      .p-listbox-option-group + .p-listbox-option-group {
        margin-top: 24px;
      }

      .p-listbox-option {
        width: var(--rd-list-item-size);
        margin-bottom: 6px;
        align-items: flex-start;
        text-overflow: ellipsis;
      }
    }
  }
}

.rd-listbox-grid {
  background-color: red;

  .p-listbox {
    width: 100%;

    .p-listbox-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: 16px;
      row-gap: 2px;
      padding: 8px;

      .p-listbox-option-group {
        grid-column: 1 / -1; // Start on new row spanning all columns
        padding: 4px 8px;
        border-radius: 4px;
        border-bottom: 1px solid var(--tw-gray-200);
        margin-bottom: 4px;
      }

      .p-listbox-option-group + .p-listbox-option-group {
        margin-top: 64px;
      }

      .p-listbox-option {
        // Automatically fills grid cells
        margin-bottom: 8px;
      }
    }
  }
}
