import { CommonModule } from '@angular/common';
import { Component, computed, inject, resource, signal, input, output, model } from '@angular/core';
import { RadPanel } from '@tec/rad-ui/layout';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'rad-edit-panel',
  exportAs: 'editPanel',
  template: `
    <rad-panel [title]="title()">
      <ng-container radPanelActions>
        @if(this.notEditing()){
        <p-button size="small" severity="secondary" (click)="onEdit()" [label]="editLabel()"></p-button>
        }
      </ng-container>

      <div class="flex flex-col gap-y-6">
        <ng-content></ng-content>
      </div>

      @if(this.editing()){
      <div class="flex flex-row sm:flex-row sm:items-center justify-end gap-4 p-4">
        <!-- Cancel -->
        <p-button severity="secondary" size="small" label="Cancel" (click)="onCancel()"> </p-button>
        <!-- Send -->
        <p-button severity="primary" size="small" [label]="updateLabel()" (click)="onUpdate()"> </p-button>
      </div>
      }
    </rad-panel>
  `,
  styles: [],
  imports: [CommonModule, RadPanel, ButtonModule],
})
export class RadEditPanel {
  title = input('');
  editing = model(false);
  editLabel = input('Edit');
  updateLabel = input('Update');
  update = output();
  revert = output();
  notEditing = computed(() => !this.editing());
  editMode = computed(() => this.editing() ? 'edit' : 'info')    ;

  protected onEdit() {
    this.editing.set(true);
  }

  protected onCancel() {
    this.revert.emit();
    this.editing.set(false);
  }

  protected onUpdate() {
    this.update.emit();
    
  }
}
