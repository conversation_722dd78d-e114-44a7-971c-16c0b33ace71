{"name": "ed-admin-course-runs", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/admin/course-runs/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/admin/course-runs/ng-package.json", "tsConfig": "a-ecademy/admin/course-runs/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/admin/course-runs/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "lint": {"executor": "@nx/eslint:lint"}}}