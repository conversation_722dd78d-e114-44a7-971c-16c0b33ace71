import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { TabItem } from './tab-item';



@Injectable(
    { providedIn: 'root' }
)
export class TabWatcher {
    private _tabsSource: BehaviorSubject<TabItem[]>;



    get tabs$() {
        return this._tabsSource.asObservable();
    }

    get tabs() {
        return this._tabsSource.value;
    }

    changeTab$: Subject<TabItem> = new Subject<TabItem>();

    closeTab$: Subject<TabItem> = new Subject<TabItem>();




    constructor() {
        this._tabsSource = new BehaviorSubject<TabItem[]>([]);
    }

    public updateTabs(tabs: TabItem[]) {
        this._tabsSource.next(tabs);
    }

    goToTab(tab: TabItem): void {
        this.changeTab$.next(tab);
    }

    closeTab(tab: TabItem): void {
        this.closeTab$.next(tab);
    }

    findTabWithUrl(url: string): TabItem {
        for(const tab of this.tabs) {
            if(tab.currentUrl === url) {
                return tab;
            }
        }
        return undefined;
    }
}
