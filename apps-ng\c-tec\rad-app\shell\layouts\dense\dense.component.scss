rad-sidebar {

    .rad-sidebar-wrapper{
        .rad-sidebar-content {
            padding-top: 12px;
        }
    }

    .logo-text {
        display: none;
    }



    /* Dense appearance overrides */
    &.rad-sidebar-appearance-dense { 

        &:not(.rad-sidebar-hover):not(.rad-sidebar-mode-over) {
            .collapse-button {
                display: none;
            }
        }
    }
    &.rad-sidebar-hover {
        .logo-text {
            display:flex;
        }
        .logo-image {
            padding-left: 8px;
        }
    }
    &.rad-sidebar-appearance-default {
        .logo-text {
            display: flex;
        }
        .logo-image {
            padding-left: 8px;
        }
    }
}

.bg-midnight {
    background-color: #0C2556 !important;
}

.rad-sidebar-content{
    padding-top: 12px;
}

.app-header {
    

    border-bottom: 1px solid var(--color-slate-300);


}