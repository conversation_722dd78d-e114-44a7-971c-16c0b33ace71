<div
  class="flex flex-col flex-auto items-center sm:justify-center min-w-0 md:p-8"
>
  <div
    class="flex md:w-full md:max-w-6xl sm:rounded-2xl sm:shadow overflow-hidden sm:bg-card"
  >
    <div
      class="relative hidden md:flex flex-auto items-center justify-center h-full p-16 lg:px-28 overflow-hidden bg-gray-800 dark:border-r"
    >
      <!-- Background - @formatter:off -->
      <!-- Rings -->
      <svg
        class="absolute inset-0 pointer-events-none"
        viewBox="0 0 960 540"
        width="100%"
        height="100%"
        preserveAspectRatio="xMidYMax slice"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          class="text-gray-700 opacity-25"
          fill="none"
          stroke="currentColor"
          stroke-width="100"
        >
          <circle r="234" cx="196" cy="23"></circle>
          <circle r="234" cx="790" cy="491"></circle>
        </g>
      </svg>
      <!-- Dots -->
      <svg
        class="absolute -top-16 -right-16 text-gray-700"
        viewBox="0 0 220 192"
        width="220"
        height="192"
        fill="none"
      >
        <defs>
          <pattern
            id="837c3e70-6c3a-44e6-8854-cc48c737b659"
            x="0"
            y="0"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <rect x="0" y="0" width="4" height="4" fill="currentColor"></rect>
          </pattern>
        </defs>
        <rect
          width="220"
          height="192"
          fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"
        ></rect>
      </svg>
      <!-- @formatter:on -->
      <!-- Content -->
      <div class="z-10 relative w-full max-w-2xl">
        <div class="text-7xl font-bold leading-none text-gray-100">
          <div>Welcome to</div>
          <div>{{applicationName}}</div>
        </div>
      </div>
    </div>
    <div class="w-full sm:w-auto py-8 px-4 sm:p-12 md:p-16 bg-card">
      <div class="w-full max-w-80 sm:w-80 mx-auto sm:mx-0">
        <!-- Logo -->
        <div class="w-16">
          <img [src]="logo" />
        </div>

        <!-- Title -->
        <div class="mt-8 text-4xl font-extrabold tracking-tight leading-tight">
          Sign in
        </div>
        <div class="flex items-baseline mt-0.5 font-medium">
          <div>Don't have an account?</div>
          <a
            class="ml-1 text-primary-500 hover:underline"
            [routerLink]="['/account/sign-up']"
            >Sign up
          </a>
        </div>

        <p-message class="w-full" *ngIf="showAlert" severity="{{alert.severity}}" text="{{alert.message}}"></p-message>
       

        <!-- Sign in form -->
        <form class="mt-8" [formGroup]="signInForm" #signInNgForm="ngForm">
          <rad-form-field label="Email Address"  >
            <input type="text" pInputText formControlName="email" fluid />
          </rad-form-field>
          <rad-form-field label="Password"   >
            <p-password formControlName="password" [feedback]="false" [toggleMask]="true" fluid />
          </rad-form-field>

          <!-- Actions -->
          <div class="flex flex-row items-center justify-between">
            <div class="flex items-center gap-1">
              <p-checkbox [binary]="true" formControlName="rememberMe" value="Remember me" label="Remember me" fluid />
              <label for="rememberMe" class="ml-2"> Remember Me </label>
            </div>
            
            <a
                class="text-md font-medium text-primary-500 hover:underline"
                [routerLink]="['/auth/forgot-password']"
                >Forgot password?
              </a>
          </div>
          <!-- Submit button -->
          <div class="flex flex-row justify-end mt-5">
            <p-button
              
            
            [disabled]="signInForm.disabled"
            (click)="signIn()"
            label="Sign In"
            [loading]="signInForm.disabled"
          >
          
          </p-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
