//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.5.0.0 (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

export const RAD_ABP_API_URL = new InjectionToken<string>('RAD_ABP_API_URL');

export interface ISessionApiProxy {
    /**
     * @return OK
     */
    getCurrentLoginInformations(): Observable<GetCurrentLoginInformationsResult>;
}

@Injectable({
    providedIn: 'root'
})
export class SessionApiProxy implements ISessionApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_ABP_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return OK
     */
    getCurrentLoginInformations(): Observable<GetCurrentLoginInformationsResult> {
        let url_ = this.baseUrl + "/api/rad-abp/session/get-current-login-informations";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCurrentLoginInformations(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCurrentLoginInformations(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetCurrentLoginInformationsResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetCurrentLoginInformationsResult>;
        }));
    }

    protected processGetCurrentLoginInformations(response: HttpResponseBase): Observable<GetCurrentLoginInformationsResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetCurrentLoginInformationsResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class ApplicationInfoDto implements IApplicationInfoDto {
    version: string | undefined;
    releaseDate: Date;
    currency: string | undefined;
    currencySign: string | undefined;
    features: { [key: string]: boolean; } | undefined;

    constructor(data?: Partial<IApplicationInfoDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.version = _data["version"];
            this.releaseDate = _data["releaseDate"] ? new Date(_data["releaseDate"].toString()) : undefined as any;
            this.currency = _data["currency"];
            this.currencySign = _data["currencySign"];
            if (_data["features"]) {
                this.features = {} as any;
                for (let key in _data["features"]) {
                    if (_data["features"].hasOwnProperty(key))
                        (this.features as any)![key] = _data["features"][key];
                }
            }
        }
    }

    static fromJS(data: any): ApplicationInfoDto {
        data = typeof data === 'object' ? data : {};
        let result = new ApplicationInfoDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["version"] = this.version;
        data["releaseDate"] = this.releaseDate ? this.releaseDate.toISOString() : undefined as any;
        data["currency"] = this.currency;
        data["currencySign"] = this.currencySign;
        if (this.features) {
            data["features"] = {};
            for (let key in this.features) {
                if (this.features.hasOwnProperty(key))
                    (data["features"] as any)[key] = (this.features as any)[key];
            }
        }
        return data;
    }

    clone(): ApplicationInfoDto {
        const json = this.toJSON();
        let result = new ApplicationInfoDto();
        result.init(json);
        return result;
    }
}

export interface IApplicationInfoDto {
    version: string | undefined;
    releaseDate: Date;
    currency: string | undefined;
    currencySign: string | undefined;
    features: { [key: string]: boolean; } | undefined;
}

export class GetCurrentLoginInformationsResult implements IGetCurrentLoginInformationsResult {
    user: UserLoginInfoDto;
    tenant: TenantLoginInfoDto;
    application: ApplicationInfoDto;

    constructor(data?: Partial<IGetCurrentLoginInformationsResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.user = _data["user"] ? UserLoginInfoDto.fromJS(_data["user"]) : undefined as any;
            this.tenant = _data["tenant"] ? TenantLoginInfoDto.fromJS(_data["tenant"]) : undefined as any;
            this.application = _data["application"] ? ApplicationInfoDto.fromJS(_data["application"]) : undefined as any;
        }
    }

    static fromJS(data: any): GetCurrentLoginInformationsResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetCurrentLoginInformationsResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["user"] = this.user ? this.user.toJSON() : undefined as any;
        data["tenant"] = this.tenant ? this.tenant.toJSON() : undefined as any;
        data["application"] = this.application ? this.application.toJSON() : undefined as any;
        return data;
    }

    clone(): GetCurrentLoginInformationsResult {
        const json = this.toJSON();
        let result = new GetCurrentLoginInformationsResult();
        result.init(json);
        return result;
    }
}

export interface IGetCurrentLoginInformationsResult {
    user: UserLoginInfoDto;
    tenant: TenantLoginInfoDto;
    application: ApplicationInfoDto;
}

export class RemoteServiceErrorInfo implements IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;

    constructor(data?: Partial<IRemoteServiceErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.message = _data["message"];
            this.details = _data["details"];
            if (_data["data"]) {
                this.data = {} as any;
                for (let key in _data["data"]) {
                    if (_data["data"].hasOwnProperty(key))
                        (this.data as any)![key] = _data["data"][key];
                }
            }
            if (Array.isArray(_data["validationErrors"])) {
                this.validationErrors = [] as any;
                for (let item of _data["validationErrors"])
                    this.validationErrors!.push(RemoteServiceValidationErrorInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): RemoteServiceErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["message"] = this.message;
        data["details"] = this.details;
        if (this.data) {
            data["data"] = {};
            for (let key in this.data) {
                if (this.data.hasOwnProperty(key))
                    (data["data"] as any)[key] = (this.data as any)[key];
            }
        }
        if (Array.isArray(this.validationErrors)) {
            data["validationErrors"] = [];
            for (let item of this.validationErrors)
                data["validationErrors"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): RemoteServiceErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;
}

export class RemoteServiceErrorResponse implements IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;

    constructor(data?: Partial<IRemoteServiceErrorResponse>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.error = _data["error"] ? RemoteServiceErrorInfo.fromJS(_data["error"]) : undefined as any;
        }
    }

    static fromJS(data: any): RemoteServiceErrorResponse {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        return data;
    }

    clone(): RemoteServiceErrorResponse {
        const json = this.toJSON();
        let result = new RemoteServiceErrorResponse();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;
}

export class RemoteServiceValidationErrorInfo implements IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;

    constructor(data?: Partial<IRemoteServiceValidationErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.message = _data["message"];
            if (Array.isArray(_data["members"])) {
                this.members = [] as any;
                for (let item of _data["members"])
                    this.members!.push(item);
            }
        }
    }

    static fromJS(data: any): RemoteServiceValidationErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceValidationErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["message"] = this.message;
        if (Array.isArray(this.members)) {
            data["members"] = [];
            for (let item of this.members)
                data["members"].push(item);
        }
        return data;
    }

    clone(): RemoteServiceValidationErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceValidationErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;
}

export class TenantLoginInfoDto implements ITenantLoginInfoDto {
    id: string;
    name: string | undefined;

    constructor(data?: Partial<ITenantLoginInfoDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
        }
    }

    static fromJS(data: any): TenantLoginInfoDto {
        data = typeof data === 'object' ? data : {};
        let result = new TenantLoginInfoDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        return data;
    }

    clone(): TenantLoginInfoDto {
        const json = this.toJSON();
        let result = new TenantLoginInfoDto();
        result.init(json);
        return result;
    }
}

export interface ITenantLoginInfoDto {
    id: string;
    name: string | undefined;
}

export class UserLoginInfoDto implements IUserLoginInfoDto {
    id: string;
    name: string | undefined;
    surname: string | undefined;
    userName: string | undefined;
    emailAddress: string | undefined;
    profilePictureId: string | undefined;

    constructor(data?: Partial<IUserLoginInfoDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.surname = _data["surname"];
            this.userName = _data["userName"];
            this.emailAddress = _data["emailAddress"];
            this.profilePictureId = _data["profilePictureId"];
        }
    }

    static fromJS(data: any): UserLoginInfoDto {
        data = typeof data === 'object' ? data : {};
        let result = new UserLoginInfoDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["surname"] = this.surname;
        data["userName"] = this.userName;
        data["emailAddress"] = this.emailAddress;
        data["profilePictureId"] = this.profilePictureId;
        return data;
    }

    clone(): UserLoginInfoDto {
        const json = this.toJSON();
        let result = new UserLoginInfoDto();
        result.init(json);
        return result;
    }
}

export interface IUserLoginInfoDto {
    id: string;
    name: string | undefined;
    surname: string | undefined;
    userName: string | undefined;
    emailAddress: string | undefined;
    profilePictureId: string | undefined;
}

export class ApiException extends Error {
    message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}