import { Component } from '@angular/core';
import { FieldType, FormlyFieldConfig } from '@ngx-formly/core';

@Component({
  selector: 'rad-formly-separator',
  template: `
    <div class="mt-4 pt-2 pb-3 flex flex-col border-t border-slate-200">
      @if(props.label){
      <span   class="text-sm font-semibold text-gray-900">
        {{ props.label }}
      </span>
      }
    </div>

  `,
  
})
export class SeparatorTypeComponent extends FieldType<FormlyFieldConfig> {}