//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v13.19.0.0 (NJsonSchema v10.9.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

import { DateTime, Duration } from "luxon";

export const XD_PORTAL_REGISTRY_API_URL = new InjectionToken<string>('XD_PORTAL_REGISTRY_API_URL');

export interface ICatalogApiProxy {
    /**
     * @param isActive (optional) 
     * @return Success
     */
    getActiveCourseListings(isActive?: boolean | undefined): Observable<GetActiveCourseListingsResult>;
}

@Injectable({
    providedIn: 'root'
})
export class CatalogApiProxy implements ICatalogApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(XD_PORTAL_REGISTRY_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "";
    }

    /**
     * @param isActive (optional) 
     * @return Success
     */
    getActiveCourseListings(isActive?: boolean | undefined): Observable<GetActiveCourseListingsResult> {
        let url_ = this.baseUrl + "/api/registry/catalog/get-active-course-listings?";
        if (isActive === null)
            throw new Error("The parameter 'isActive' cannot be null.");
        else if (isActive !== undefined)
            url_ += "IsActive=" + encodeURIComponent("" + isActive) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetActiveCourseListings(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetActiveCourseListings(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetActiveCourseListingsResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetActiveCourseListingsResult>;
        }));
    }

    protected processGetActiveCourseListings(response: HttpResponseBase): Observable<GetActiveCourseListingsResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetActiveCourseListingsResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IProfileApiProxy {
    /**
     * @param body (optional) 
     * @return Success
     */
    getChildProfiles(body?: GetChildProfilesInput | undefined): Observable<GetChildProfilesOutput>;
    /**
     * @param userId (optional) 
     * @return Success
     */
    getUserProfile(userId?: string | undefined): Observable<GetUserProfileOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    updateAvatarImage(body?: UpdateAvatarImageInput | undefined): Observable<UpdateAvatarImageOutput>;
    /**
     * @param studentId (optional) 
     * @return Success
     */
    getStudentProfile(studentId?: string | undefined): Observable<GetStudentProfileOutput>;
    /**
     * @param parentId (optional) 
     * @return Success
     */
    getParentProfile(parentId?: string | undefined): Observable<GetParentProfileOutput>;
}

@Injectable({
    providedIn: 'root'
})
export class ProfileApiProxy implements IProfileApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(XD_PORTAL_REGISTRY_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "";
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    getChildProfiles(body?: GetChildProfilesInput | undefined): Observable<GetChildProfilesOutput> {
        let url_ = this.baseUrl + "/api/registry/profile/get-child-profiles";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetChildProfiles(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetChildProfiles(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetChildProfilesOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetChildProfilesOutput>;
        }));
    }

    protected processGetChildProfiles(response: HttpResponseBase): Observable<GetChildProfilesOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetChildProfilesOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param userId (optional) 
     * @return Success
     */
    getUserProfile(userId?: string | undefined): Observable<GetUserProfileOutput> {
        let url_ = this.baseUrl + "/api/registry/profile/get-user-profile?";
        if (userId === null)
            throw new Error("The parameter 'userId' cannot be null.");
        else if (userId !== undefined)
            url_ += "UserId=" + encodeURIComponent("" + userId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetUserProfile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetUserProfile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserProfileOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserProfileOutput>;
        }));
    }

    protected processGetUserProfile(response: HttpResponseBase): Observable<GetUserProfileOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserProfileOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    updateAvatarImage(body?: UpdateAvatarImageInput | undefined): Observable<UpdateAvatarImageOutput> {
        let url_ = this.baseUrl + "/api/registry/profile/update-avatar-image";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateAvatarImage(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateAvatarImage(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<UpdateAvatarImageOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<UpdateAvatarImageOutput>;
        }));
    }

    protected processUpdateAvatarImage(response: HttpResponseBase): Observable<UpdateAvatarImageOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = UpdateAvatarImageOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param studentId (optional) 
     * @return Success
     */
    getStudentProfile(studentId?: string | undefined): Observable<GetStudentProfileOutput> {
        let url_ = this.baseUrl + "/api/registry/profile/get-student-profile?";
        if (studentId === null)
            throw new Error("The parameter 'studentId' cannot be null.");
        else if (studentId !== undefined)
            url_ += "StudentId=" + encodeURIComponent("" + studentId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetStudentProfile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetStudentProfile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetStudentProfileOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetStudentProfileOutput>;
        }));
    }

    protected processGetStudentProfile(response: HttpResponseBase): Observable<GetStudentProfileOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetStudentProfileOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param parentId (optional) 
     * @return Success
     */
    getParentProfile(parentId?: string | undefined): Observable<GetParentProfileOutput> {
        let url_ = this.baseUrl + "/api/registry/profile/get-parent-profile?";
        if (parentId === null)
            throw new Error("The parameter 'parentId' cannot be null.");
        else if (parentId !== undefined)
            url_ += "ParentId=" + encodeURIComponent("" + parentId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetParentProfile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetParentProfile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetParentProfileOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetParentProfileOutput>;
        }));
    }

    protected processGetParentProfile(response: HttpResponseBase): Observable<GetParentProfileOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetParentProfileOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IRegistrationApiProxy {
    /**
     * @param body (optional) 
     * @return Success
     */
    registerChild(body?: RegisterChildInput | undefined): Observable<RegisterChildOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    registerChildInCourse(body?: RegisterChildInCourseInput | undefined): Observable<RegisterChildInCourseOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    signUpParent(body?: SignUpParentInput | undefined): Observable<SignUpParentOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    signUpParentAndChild(body?: SignUpParentAndChildInput | undefined): Observable<SignUpParentAndChildOutput>;
}

@Injectable({
    providedIn: 'root'
})
export class RegistrationApiProxy implements IRegistrationApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(XD_PORTAL_REGISTRY_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "";
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    registerChild(body?: RegisterChildInput | undefined): Observable<RegisterChildOutput> {
        let url_ = this.baseUrl + "/api/registry/registration/register-child";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRegisterChild(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRegisterChild(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<RegisterChildOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<RegisterChildOutput>;
        }));
    }

    protected processRegisterChild(response: HttpResponseBase): Observable<RegisterChildOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = RegisterChildOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    registerChildInCourse(body?: RegisterChildInCourseInput | undefined): Observable<RegisterChildInCourseOutput> {
        let url_ = this.baseUrl + "/api/registry/registration/register-child-in-course";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRegisterChildInCourse(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRegisterChildInCourse(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<RegisterChildInCourseOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<RegisterChildInCourseOutput>;
        }));
    }

    protected processRegisterChildInCourse(response: HttpResponseBase): Observable<RegisterChildInCourseOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = RegisterChildInCourseOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    signUpParent(body?: SignUpParentInput | undefined): Observable<SignUpParentOutput> {
        let url_ = this.baseUrl + "/api/registry/registration/sign-up-parent";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSignUpParent(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSignUpParent(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<SignUpParentOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<SignUpParentOutput>;
        }));
    }

    protected processSignUpParent(response: HttpResponseBase): Observable<SignUpParentOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = SignUpParentOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    signUpParentAndChild(body?: SignUpParentAndChildInput | undefined): Observable<SignUpParentAndChildOutput> {
        let url_ = this.baseUrl + "/api/registry/registration/sign-up-parent-and-child";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSignUpParentAndChild(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSignUpParentAndChild(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<SignUpParentAndChildOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<SignUpParentAndChildOutput>;
        }));
    }

    protected processSignUpParentAndChild(response: HttpResponseBase): Observable<SignUpParentAndChildOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = SignUpParentAndChildOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IRegistryApiProxy {
    /**
     * @param body (optional) 
     * @return Success
     */
    completePayment(body?: any | undefined): Observable<CompletePaymentOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    placeOrder(body?: PlaceOrderInput | undefined): Observable<PlaceOrderOutput>;
    /**
     * @param body (optional) 
     * @return Success
     */
    startPayment(body?: StartPaymentInput | undefined): Observable<StartPaymentOutput>;
}

@Injectable({
    providedIn: 'root'
})
export class RegistryApiProxy implements IRegistryApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(XD_PORTAL_REGISTRY_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "";
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    completePayment(body?: any | undefined): Observable<CompletePaymentOutput> {
        let url_ = this.baseUrl + "/api/registry/complete-payment";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCompletePayment(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCompletePayment(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CompletePaymentOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CompletePaymentOutput>;
        }));
    }

    protected processCompletePayment(response: HttpResponseBase): Observable<CompletePaymentOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CompletePaymentOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    placeOrder(body?: PlaceOrderInput | undefined): Observable<PlaceOrderOutput> {
        let url_ = this.baseUrl + "/api/registry/place-order";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processPlaceOrder(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processPlaceOrder(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<PlaceOrderOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<PlaceOrderOutput>;
        }));
    }

    protected processPlaceOrder(response: HttpResponseBase): Observable<PlaceOrderOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = PlaceOrderOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    startPayment(body?: StartPaymentInput | undefined): Observable<StartPaymentOutput> {
        let url_ = this.baseUrl + "/api/registry/start-payment";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processStartPayment(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processStartPayment(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StartPaymentOutput>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StartPaymentOutput>;
        }));
    }

    protected processStartPayment(response: HttpResponseBase): Observable<StartPaymentOutput> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StartPaymentOutput.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Server Error", status, _responseText, _headers, result501);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class CompletePaymentOutput implements ICompletePaymentOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;

    constructor(data?: ICompletePaymentOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
        }
    }

    static fromJS(data: any): CompletePaymentOutput {
        data = typeof data === 'object' ? data : {};
        let result = new CompletePaymentOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        return data;
    }

    clone(): CompletePaymentOutput {
        const json = this.toJSON();
        let result = new CompletePaymentOutput();
        result.init(json);
        return result;
    }
}

export interface ICompletePaymentOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
}

export class CourseListingInfo implements ICourseListingInfo {
    id!: string;
    name!: string | undefined;
    description!: string | undefined;
    requirements!: string | undefined;
    gradeLevel!: string | undefined;
    listingStatus!: ListingStatus;
    registrationStatus!: CourseRegistrationStatus;
    startDate!: DateTime | undefined;
    schoolYear!: string | undefined;

    constructor(data?: ICourseListingInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.description = _data["description"];
            this.requirements = _data["requirements"];
            this.gradeLevel = _data["gradeLevel"];
            this.listingStatus = _data["listingStatus"];
            this.registrationStatus = _data["registrationStatus"];
            this.startDate = _data["startDate"] ? DateTime.fromISO(_data["startDate"].toString()) : <any>undefined;
            this.schoolYear = _data["schoolYear"];
        }
    }

    static fromJS(data: any): CourseListingInfo {
        data = typeof data === 'object' ? data : {};
        let result = new CourseListingInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["description"] = this.description;
        data["requirements"] = this.requirements;
        data["gradeLevel"] = this.gradeLevel;
        data["listingStatus"] = this.listingStatus;
        data["registrationStatus"] = this.registrationStatus;
        data["startDate"] = this.startDate ? this.startDate.toFormat('yyyy-MM-dd') : <any>undefined;
        data["schoolYear"] = this.schoolYear;
        return data;
    }

    clone(): CourseListingInfo {
        const json = this.toJSON();
        let result = new CourseListingInfo();
        result.init(json);
        return result;
    }
}

export interface ICourseListingInfo {
    id: string;
    name: string | undefined;
    description: string | undefined;
    requirements: string | undefined;
    gradeLevel: string | undefined;
    listingStatus: ListingStatus;
    registrationStatus: CourseRegistrationStatus;
    startDate: DateTime | undefined;
    schoolYear: string | undefined;
}

export enum CourseRegistrationStatus {
    Open = "Open",
    Closed = "Closed",
}

export enum CourseRunStatus {
    Scheduled = "Scheduled",
    InProgress = "InProgress",
    Completed = "Completed",
}

export class GetActiveCourseListingsResult implements IGetActiveCourseListingsResult {
    items!: CourseListingInfo[] | undefined;

    constructor(data?: IGetActiveCourseListingsResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(CourseListingInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetActiveCourseListingsResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetActiveCourseListingsResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item.toJSON());
        }
        return data;
    }

    clone(): GetActiveCourseListingsResult {
        const json = this.toJSON();
        let result = new GetActiveCourseListingsResult();
        result.init(json);
        return result;
    }
}

export interface IGetActiveCourseListingsResult {
    items: CourseListingInfo[] | undefined;
}

export class GetChildProfilesInput implements IGetChildProfilesInput {
    parentId!: string;

    constructor(data?: IGetChildProfilesInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.parentId = _data["parentId"];
        }
    }

    static fromJS(data: any): GetChildProfilesInput {
        data = typeof data === 'object' ? data : {};
        let result = new GetChildProfilesInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["parentId"] = this.parentId;
        return data;
    }

    clone(): GetChildProfilesInput {
        const json = this.toJSON();
        let result = new GetChildProfilesInput();
        result.init(json);
        return result;
    }
}

export interface IGetChildProfilesInput {
    parentId: string;
}

export class GetChildProfilesOutput implements IGetChildProfilesOutput {
    items!: StudentProfileDto[] | undefined;

    constructor(data?: IGetChildProfilesOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(StudentProfileDto.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetChildProfilesOutput {
        data = typeof data === 'object' ? data : {};
        let result = new GetChildProfilesOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item.toJSON());
        }
        return data;
    }

    clone(): GetChildProfilesOutput {
        const json = this.toJSON();
        let result = new GetChildProfilesOutput();
        result.init(json);
        return result;
    }
}

export interface IGetChildProfilesOutput {
    items: StudentProfileDto[] | undefined;
}

export class GetParentProfileOutput implements IGetParentProfileOutput {
    children!: ParentChildInfo[] | undefined;

    constructor(data?: IGetParentProfileOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["children"])) {
                this.children = [] as any;
                for (let item of _data["children"])
                    this.children!.push(ParentChildInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetParentProfileOutput {
        data = typeof data === 'object' ? data : {};
        let result = new GetParentProfileOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.children)) {
            data["children"] = [];
            for (let item of this.children)
                data["children"].push(item.toJSON());
        }
        return data;
    }

    clone(): GetParentProfileOutput {
        const json = this.toJSON();
        let result = new GetParentProfileOutput();
        result.init(json);
        return result;
    }
}

export interface IGetParentProfileOutput {
    children: ParentChildInfo[] | undefined;
}

export class GetStudentProfileOutput implements IGetStudentProfileOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    profile!: StudentProfileDto;
    subscriptions!: StudentSubscriptionInfo[] | undefined;
    currentSubscription!: StudentSubscriptionInfo;

    constructor(data?: IGetStudentProfileOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.profile = _data["profile"] ? StudentProfileDto.fromJS(_data["profile"]) : <any>undefined;
            if (Array.isArray(_data["subscriptions"])) {
                this.subscriptions = [] as any;
                for (let item of _data["subscriptions"])
                    this.subscriptions!.push(StudentSubscriptionInfo.fromJS(item));
            }
            this.currentSubscription = _data["currentSubscription"] ? StudentSubscriptionInfo.fromJS(_data["currentSubscription"]) : <any>undefined;
        }
    }

    static fromJS(data: any): GetStudentProfileOutput {
        data = typeof data === 'object' ? data : {};
        let result = new GetStudentProfileOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["profile"] = this.profile ? this.profile.toJSON() : <any>undefined;
        if (Array.isArray(this.subscriptions)) {
            data["subscriptions"] = [];
            for (let item of this.subscriptions)
                data["subscriptions"].push(item.toJSON());
        }
        data["currentSubscription"] = this.currentSubscription ? this.currentSubscription.toJSON() : <any>undefined;
        return data;
    }

    clone(): GetStudentProfileOutput {
        const json = this.toJSON();
        let result = new GetStudentProfileOutput();
        result.init(json);
        return result;
    }
}

export interface IGetStudentProfileOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    profile: StudentProfileDto;
    subscriptions: StudentSubscriptionInfo[] | undefined;
    currentSubscription: StudentSubscriptionInfo;
}

export class GetUserProfileOutput implements IGetUserProfileOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    profile!: UserProfileInfo;

    constructor(data?: IGetUserProfileOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.profile = _data["profile"] ? UserProfileInfo.fromJS(_data["profile"]) : <any>undefined;
        }
    }

    static fromJS(data: any): GetUserProfileOutput {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserProfileOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["profile"] = this.profile ? this.profile.toJSON() : <any>undefined;
        return data;
    }

    clone(): GetUserProfileOutput {
        const json = this.toJSON();
        let result = new GetUserProfileOutput();
        result.init(json);
        return result;
    }
}

export interface IGetUserProfileOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    profile: UserProfileInfo;
}

export class IError implements IIError {
    readonly code!: string | undefined;
    readonly description!: string | undefined;

    constructor(data?: IIError) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).code = _data["code"];
            (<any>this).description = _data["description"];
        }
    }

    static fromJS(data: any): IError {
        data = typeof data === 'object' ? data : {};
        let result = new IError();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["description"] = this.description;
        return data;
    }

    clone(): IError {
        const json = this.toJSON();
        let result = new IError();
        result.init(json);
        return result;
    }
}

export interface IIError {
    code: string | undefined;
    description: string | undefined;
}

export enum ListingStatus {
    Inactive = "Inactive",
    Preview = "Preview",
    Active = "Active",
}

export class ParentChildInfo implements IParentChildInfo {
    parentUserId!: string;
    childUserId!: string;
    name!: string | undefined;

    constructor(data?: IParentChildInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.parentUserId = _data["parentUserId"];
            this.childUserId = _data["childUserId"];
            this.name = _data["name"];
        }
    }

    static fromJS(data: any): ParentChildInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ParentChildInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["parentUserId"] = this.parentUserId;
        data["childUserId"] = this.childUserId;
        data["name"] = this.name;
        return data;
    }

    clone(): ParentChildInfo {
        const json = this.toJSON();
        let result = new ParentChildInfo();
        result.init(json);
        return result;
    }
}

export interface IParentChildInfo {
    parentUserId: string;
    childUserId: string;
    name: string | undefined;
}

export class PlaceOrderInput implements IPlaceOrderInput {
    subscriptions!: SubscriptionOrder[] | undefined;

    constructor(data?: IPlaceOrderInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["subscriptions"])) {
                this.subscriptions = [] as any;
                for (let item of _data["subscriptions"])
                    this.subscriptions!.push(SubscriptionOrder.fromJS(item));
            }
        }
    }

    static fromJS(data: any): PlaceOrderInput {
        data = typeof data === 'object' ? data : {};
        let result = new PlaceOrderInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.subscriptions)) {
            data["subscriptions"] = [];
            for (let item of this.subscriptions)
                data["subscriptions"].push(item.toJSON());
        }
        return data;
    }

    clone(): PlaceOrderInput {
        const json = this.toJSON();
        let result = new PlaceOrderInput();
        result.init(json);
        return result;
    }
}

export interface IPlaceOrderInput {
    subscriptions: SubscriptionOrder[] | undefined;
}

export class PlaceOrderOutput implements IPlaceOrderOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    orderId!: string;
    amount!: number;

    constructor(data?: IPlaceOrderOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.orderId = _data["orderId"];
            this.amount = _data["amount"];
        }
    }

    static fromJS(data: any): PlaceOrderOutput {
        data = typeof data === 'object' ? data : {};
        let result = new PlaceOrderOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["orderId"] = this.orderId;
        data["amount"] = this.amount;
        return data;
    }

    clone(): PlaceOrderOutput {
        const json = this.toJSON();
        let result = new PlaceOrderOutput();
        result.init(json);
        return result;
    }
}

export interface IPlaceOrderOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    orderId: string;
    amount: number;
}

export class RegisterChildInCourseInput implements IRegisterChildInCourseInput {
    parentUserId!: string | undefined;
    details!: StudentRegistrationDetails;
    password!: string | undefined;
    courseRunId!: string;
    subscriptionPlanId!: string | undefined;

    constructor(data?: IRegisterChildInCourseInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.parentUserId = _data["parentUserId"];
            this.details = _data["details"] ? StudentRegistrationDetails.fromJS(_data["details"]) : <any>undefined;
            this.password = _data["password"];
            this.courseRunId = _data["courseRunId"];
            this.subscriptionPlanId = _data["subscriptionPlanId"];
        }
    }

    static fromJS(data: any): RegisterChildInCourseInput {
        data = typeof data === 'object' ? data : {};
        let result = new RegisterChildInCourseInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["parentUserId"] = this.parentUserId;
        data["details"] = this.details ? this.details.toJSON() : <any>undefined;
        data["password"] = this.password;
        data["courseRunId"] = this.courseRunId;
        data["subscriptionPlanId"] = this.subscriptionPlanId;
        return data;
    }

    clone(): RegisterChildInCourseInput {
        const json = this.toJSON();
        let result = new RegisterChildInCourseInput();
        result.init(json);
        return result;
    }
}

export interface IRegisterChildInCourseInput {
    parentUserId: string | undefined;
    details: StudentRegistrationDetails;
    password: string | undefined;
    courseRunId: string;
    subscriptionPlanId: string | undefined;
}

export class RegisterChildInCourseOutput implements IRegisterChildInCourseOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    registrationId!: string;
    subscriptionId!: string;

    constructor(data?: IRegisterChildInCourseOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.registrationId = _data["registrationId"];
            this.subscriptionId = _data["subscriptionId"];
        }
    }

    static fromJS(data: any): RegisterChildInCourseOutput {
        data = typeof data === 'object' ? data : {};
        let result = new RegisterChildInCourseOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["registrationId"] = this.registrationId;
        data["subscriptionId"] = this.subscriptionId;
        return data;
    }

    clone(): RegisterChildInCourseOutput {
        const json = this.toJSON();
        let result = new RegisterChildInCourseOutput();
        result.init(json);
        return result;
    }
}

export interface IRegisterChildInCourseOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    registrationId: string;
    subscriptionId: string;
}

export class RegisterChildInput implements IRegisterChildInput {
    parentUserId!: string | undefined;
    details!: StudentRegistrationDetails;
    password!: string | undefined;
    courseRunId!: string;
    subscriptionPlanId!: string | undefined;

    constructor(data?: IRegisterChildInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.parentUserId = _data["parentUserId"];
            this.details = _data["details"] ? StudentRegistrationDetails.fromJS(_data["details"]) : <any>undefined;
            this.password = _data["password"];
            this.courseRunId = _data["courseRunId"];
            this.subscriptionPlanId = _data["subscriptionPlanId"];
        }
    }

    static fromJS(data: any): RegisterChildInput {
        data = typeof data === 'object' ? data : {};
        let result = new RegisterChildInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["parentUserId"] = this.parentUserId;
        data["details"] = this.details ? this.details.toJSON() : <any>undefined;
        data["password"] = this.password;
        data["courseRunId"] = this.courseRunId;
        data["subscriptionPlanId"] = this.subscriptionPlanId;
        return data;
    }

    clone(): RegisterChildInput {
        const json = this.toJSON();
        let result = new RegisterChildInput();
        result.init(json);
        return result;
    }
}

export interface IRegisterChildInput {
    parentUserId: string | undefined;
    details: StudentRegistrationDetails;
    password: string | undefined;
    courseRunId: string;
    subscriptionPlanId: string | undefined;
}

export class RegisterChildOutput implements IRegisterChildOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    registrationId!: string;
    subscriptionId!: string;
    studentUserId!: string;

    constructor(data?: IRegisterChildOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.registrationId = _data["registrationId"];
            this.subscriptionId = _data["subscriptionId"];
            this.studentUserId = _data["studentUserId"];
        }
    }

    static fromJS(data: any): RegisterChildOutput {
        data = typeof data === 'object' ? data : {};
        let result = new RegisterChildOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["registrationId"] = this.registrationId;
        data["subscriptionId"] = this.subscriptionId;
        data["studentUserId"] = this.studentUserId;
        return data;
    }

    clone(): RegisterChildOutput {
        const json = this.toJSON();
        let result = new RegisterChildOutput();
        result.init(json);
        return result;
    }
}

export interface IRegisterChildOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    registrationId: string;
    subscriptionId: string;
    studentUserId: string;
}

export class RemoteServiceErrorInfo implements IRemoteServiceErrorInfo {
    code!: string | undefined;
    message!: string | undefined;
    details!: string | undefined;
    data!: { [key: string]: any; } | undefined;
    validationErrors!: RemoteServiceValidationErrorInfo[] | undefined;

    constructor(data?: IRemoteServiceErrorInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.message = _data["message"];
            this.details = _data["details"];
            if (_data["data"]) {
                this.data = {} as any;
                for (let key in _data["data"]) {
                    if (_data["data"].hasOwnProperty(key))
                        (<any>this.data)![key] = _data["data"][key];
                }
            }
            if (Array.isArray(_data["validationErrors"])) {
                this.validationErrors = [] as any;
                for (let item of _data["validationErrors"])
                    this.validationErrors!.push(RemoteServiceValidationErrorInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): RemoteServiceErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["message"] = this.message;
        data["details"] = this.details;
        if (this.data) {
            data["data"] = {};
            for (let key in this.data) {
                if (this.data.hasOwnProperty(key))
                    (<any>data["data"])[key] = (<any>this.data)[key];
            }
        }
        if (Array.isArray(this.validationErrors)) {
            data["validationErrors"] = [];
            for (let item of this.validationErrors)
                data["validationErrors"].push(item.toJSON());
        }
        return data;
    }

    clone(): RemoteServiceErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;
}

export class RemoteServiceErrorResponse implements IRemoteServiceErrorResponse {
    error!: RemoteServiceErrorInfo;

    constructor(data?: IRemoteServiceErrorResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.error = _data["error"] ? RemoteServiceErrorInfo.fromJS(_data["error"]) : <any>undefined;
        }
    }

    static fromJS(data: any): RemoteServiceErrorResponse {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["error"] = this.error ? this.error.toJSON() : <any>undefined;
        return data;
    }

    clone(): RemoteServiceErrorResponse {
        const json = this.toJSON();
        let result = new RemoteServiceErrorResponse();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;
}

export class RemoteServiceValidationErrorInfo implements IRemoteServiceValidationErrorInfo {
    message!: string | undefined;
    members!: string[] | undefined;

    constructor(data?: IRemoteServiceValidationErrorInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.message = _data["message"];
            if (Array.isArray(_data["members"])) {
                this.members = [] as any;
                for (let item of _data["members"])
                    this.members!.push(item);
            }
        }
    }

    static fromJS(data: any): RemoteServiceValidationErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceValidationErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["message"] = this.message;
        if (Array.isArray(this.members)) {
            data["members"] = [];
            for (let item of this.members)
                data["members"].push(item);
        }
        return data;
    }

    clone(): RemoteServiceValidationErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceValidationErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;
}

export class SignUpParentAndChildInput implements ISignUpParentAndChildInput {
    parentFirstName!: string | undefined;
    parentLastName!: string | undefined;
    parentEmailAddress!: string | undefined;
    parentPhone!: string | undefined;
    studentFirstName!: string | undefined;
    studentLastName!: string | undefined;
    studentEmailAddress!: string | undefined;
    studentUsername!: string | undefined;
    gender!: string | undefined;
    dateOfBirth!: DateTime;
    courseId!: string;
    itemPlanId!: string | undefined;
    currentSchool!: string | undefined;
    otherSchool!: string | undefined;
    source!: string | undefined;

    constructor(data?: ISignUpParentAndChildInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.parentFirstName = _data["parentFirstName"];
            this.parentLastName = _data["parentLastName"];
            this.parentEmailAddress = _data["parentEmailAddress"];
            this.parentPhone = _data["parentPhone"];
            this.studentFirstName = _data["studentFirstName"];
            this.studentLastName = _data["studentLastName"];
            this.studentEmailAddress = _data["studentEmailAddress"];
            this.studentUsername = _data["studentUsername"];
            this.gender = _data["gender"];
            this.dateOfBirth = _data["dateOfBirth"] ? DateTime.fromISO(_data["dateOfBirth"].toString()) : <any>undefined;
            this.courseId = _data["courseId"];
            this.itemPlanId = _data["itemPlanId"];
            this.currentSchool = _data["currentSchool"];
            this.otherSchool = _data["otherSchool"];
            this.source = _data["source"];
        }
    }

    static fromJS(data: any): SignUpParentAndChildInput {
        data = typeof data === 'object' ? data : {};
        let result = new SignUpParentAndChildInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["parentFirstName"] = this.parentFirstName;
        data["parentLastName"] = this.parentLastName;
        data["parentEmailAddress"] = this.parentEmailAddress;
        data["parentPhone"] = this.parentPhone;
        data["studentFirstName"] = this.studentFirstName;
        data["studentLastName"] = this.studentLastName;
        data["studentEmailAddress"] = this.studentEmailAddress;
        data["studentUsername"] = this.studentUsername;
        data["gender"] = this.gender;
        data["dateOfBirth"] = this.dateOfBirth ? this.dateOfBirth.toFormat('yyyy-MM-dd') : <any>undefined;
        data["courseId"] = this.courseId;
        data["itemPlanId"] = this.itemPlanId;
        data["currentSchool"] = this.currentSchool;
        data["otherSchool"] = this.otherSchool;
        data["source"] = this.source;
        return data;
    }

    clone(): SignUpParentAndChildInput {
        const json = this.toJSON();
        let result = new SignUpParentAndChildInput();
        result.init(json);
        return result;
    }
}

export interface ISignUpParentAndChildInput {
    parentFirstName: string | undefined;
    parentLastName: string | undefined;
    parentEmailAddress: string | undefined;
    parentPhone: string | undefined;
    studentFirstName: string | undefined;
    studentLastName: string | undefined;
    studentEmailAddress: string | undefined;
    studentUsername: string | undefined;
    gender: string | undefined;
    dateOfBirth: DateTime;
    courseId: string;
    itemPlanId: string | undefined;
    currentSchool: string | undefined;
    otherSchool: string | undefined;
    source: string | undefined;
}

export class SignUpParentAndChildOutput implements ISignUpParentAndChildOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    registrationId!: string;
    subscriptionId!: string;

    constructor(data?: ISignUpParentAndChildOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.registrationId = _data["registrationId"];
            this.subscriptionId = _data["subscriptionId"];
        }
    }

    static fromJS(data: any): SignUpParentAndChildOutput {
        data = typeof data === 'object' ? data : {};
        let result = new SignUpParentAndChildOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["registrationId"] = this.registrationId;
        data["subscriptionId"] = this.subscriptionId;
        return data;
    }

    clone(): SignUpParentAndChildOutput {
        const json = this.toJSON();
        let result = new SignUpParentAndChildOutput();
        result.init(json);
        return result;
    }
}

export interface ISignUpParentAndChildOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    registrationId: string;
    subscriptionId: string;
}

export class SignUpParentInput implements ISignUpParentInput {
    password!: string | undefined;
    firstName!: string | undefined;
    lastName!: string | undefined;
    username!: string | undefined;
    emailAddress!: string | undefined;
    phoneNumber!: string | undefined;

    constructor(data?: ISignUpParentInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.password = _data["password"];
            this.firstName = _data["firstName"];
            this.lastName = _data["lastName"];
            this.username = _data["username"];
            this.emailAddress = _data["emailAddress"];
            this.phoneNumber = _data["phoneNumber"];
        }
    }

    static fromJS(data: any): SignUpParentInput {
        data = typeof data === 'object' ? data : {};
        let result = new SignUpParentInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["password"] = this.password;
        data["firstName"] = this.firstName;
        data["lastName"] = this.lastName;
        data["username"] = this.username;
        data["emailAddress"] = this.emailAddress;
        data["phoneNumber"] = this.phoneNumber;
        return data;
    }

    clone(): SignUpParentInput {
        const json = this.toJSON();
        let result = new SignUpParentInput();
        result.init(json);
        return result;
    }
}

export interface ISignUpParentInput {
    password: string | undefined;
    firstName: string | undefined;
    lastName: string | undefined;
    username: string | undefined;
    emailAddress: string | undefined;
    phoneNumber: string | undefined;
}

export class SignUpParentOutput implements ISignUpParentOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    userId!: string;

    constructor(data?: ISignUpParentOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.userId = _data["userId"];
        }
    }

    static fromJS(data: any): SignUpParentOutput {
        data = typeof data === 'object' ? data : {};
        let result = new SignUpParentOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["userId"] = this.userId;
        return data;
    }

    clone(): SignUpParentOutput {
        const json = this.toJSON();
        let result = new SignUpParentOutput();
        result.init(json);
        return result;
    }
}

export interface ISignUpParentOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    userId: string;
}

export class StartPaymentInput implements IStartPaymentInput {
    orderId!: string;
    orderNumber!: string | undefined;

    constructor(data?: IStartPaymentInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.orderId = _data["orderId"];
            this.orderNumber = _data["orderNumber"];
        }
    }

    static fromJS(data: any): StartPaymentInput {
        data = typeof data === 'object' ? data : {};
        let result = new StartPaymentInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["orderId"] = this.orderId;
        data["orderNumber"] = this.orderNumber;
        return data;
    }

    clone(): StartPaymentInput {
        const json = this.toJSON();
        let result = new StartPaymentInput();
        result.init(json);
        return result;
    }
}

export interface IStartPaymentInput {
    orderId: string;
    orderNumber: string | undefined;
}

export class StartPaymentOutput implements IStartPaymentOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;

    constructor(data?: IStartPaymentOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
        }
    }

    static fromJS(data: any): StartPaymentOutput {
        data = typeof data === 'object' ? data : {};
        let result = new StartPaymentOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        return data;
    }

    clone(): StartPaymentOutput {
        const json = this.toJSON();
        let result = new StartPaymentOutput();
        result.init(json);
        return result;
    }
}

export interface IStartPaymentOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
}

export class StudentProfileDto implements IStudentProfileDto {
    userId!: string;
    fullName!: string | undefined;
    gender!: string | undefined;
    gradeLevel!: string | undefined;

    constructor(data?: IStudentProfileDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userId = _data["userId"];
            this.fullName = _data["fullName"];
            this.gender = _data["gender"];
            this.gradeLevel = _data["gradeLevel"];
        }
    }

    static fromJS(data: any): StudentProfileDto {
        data = typeof data === 'object' ? data : {};
        let result = new StudentProfileDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userId"] = this.userId;
        data["fullName"] = this.fullName;
        data["gender"] = this.gender;
        data["gradeLevel"] = this.gradeLevel;
        return data;
    }

    clone(): StudentProfileDto {
        const json = this.toJSON();
        let result = new StudentProfileDto();
        result.init(json);
        return result;
    }
}

export interface IStudentProfileDto {
    userId: string;
    fullName: string | undefined;
    gender: string | undefined;
    gradeLevel: string | undefined;
}

export class StudentRegistrationDetails implements IStudentRegistrationDetails {
    currentSchool!: string | undefined;
    username!: string | undefined;
    email!: string | undefined;
    firstName!: string | undefined;
    lastName!: string | undefined;
    gender!: string | undefined;
    dateOfBirth!: DateTime;

    constructor(data?: IStudentRegistrationDetails) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.currentSchool = _data["currentSchool"];
            this.username = _data["username"];
            this.email = _data["email"];
            this.firstName = _data["firstName"];
            this.lastName = _data["lastName"];
            this.gender = _data["gender"];
            this.dateOfBirth = _data["dateOfBirth"] ? DateTime.fromISO(_data["dateOfBirth"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): StudentRegistrationDetails {
        data = typeof data === 'object' ? data : {};
        let result = new StudentRegistrationDetails();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["currentSchool"] = this.currentSchool;
        data["username"] = this.username;
        data["email"] = this.email;
        data["firstName"] = this.firstName;
        data["lastName"] = this.lastName;
        data["gender"] = this.gender;
        data["dateOfBirth"] = this.dateOfBirth ? this.dateOfBirth.toFormat('yyyy-MM-dd') : <any>undefined;
        return data;
    }

    clone(): StudentRegistrationDetails {
        const json = this.toJSON();
        let result = new StudentRegistrationDetails();
        result.init(json);
        return result;
    }
}

export interface IStudentRegistrationDetails {
    currentSchool: string | undefined;
    username: string | undefined;
    email: string | undefined;
    firstName: string | undefined;
    lastName: string | undefined;
    gender: string | undefined;
    dateOfBirth: DateTime;
}

export class StudentSubscriptionInfo implements IStudentSubscriptionInfo {
    userId!: string;
    courseId!: string;
    course!: string | undefined;
    runId!: string;
    run!: string | undefined;
    runStatus!: CourseRunStatus;
    runStartDate!: DateTime;
    planId!: string;
    plan!: string | undefined;

    constructor(data?: IStudentSubscriptionInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userId = _data["userId"];
            this.courseId = _data["courseId"];
            this.course = _data["course"];
            this.runId = _data["runId"];
            this.run = _data["run"];
            this.runStatus = _data["runStatus"];
            this.runStartDate = _data["runStartDate"] ? DateTime.fromISO(_data["runStartDate"].toString()) : <any>undefined;
            this.planId = _data["planId"];
            this.plan = _data["plan"];
        }
    }

    static fromJS(data: any): StudentSubscriptionInfo {
        data = typeof data === 'object' ? data : {};
        let result = new StudentSubscriptionInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userId"] = this.userId;
        data["courseId"] = this.courseId;
        data["course"] = this.course;
        data["runId"] = this.runId;
        data["run"] = this.run;
        data["runStatus"] = this.runStatus;
        data["runStartDate"] = this.runStartDate ? this.runStartDate.toFormat('yyyy-MM-dd') : <any>undefined;
        data["planId"] = this.planId;
        data["plan"] = this.plan;
        return data;
    }

    clone(): StudentSubscriptionInfo {
        const json = this.toJSON();
        let result = new StudentSubscriptionInfo();
        result.init(json);
        return result;
    }
}

export interface IStudentSubscriptionInfo {
    userId: string;
    courseId: string;
    course: string | undefined;
    runId: string;
    run: string | undefined;
    runStatus: CourseRunStatus;
    runStartDate: DateTime;
    planId: string;
    plan: string | undefined;
}

export class SubscriptionOrder implements ISubscriptionOrder {
    subscriptionId!: string;
    priceId!: string;

    constructor(data?: ISubscriptionOrder) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.subscriptionId = _data["subscriptionId"];
            this.priceId = _data["priceId"];
        }
    }

    static fromJS(data: any): SubscriptionOrder {
        data = typeof data === 'object' ? data : {};
        let result = new SubscriptionOrder();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["subscriptionId"] = this.subscriptionId;
        data["priceId"] = this.priceId;
        return data;
    }

    clone(): SubscriptionOrder {
        const json = this.toJSON();
        let result = new SubscriptionOrder();
        result.init(json);
        return result;
    }
}

export interface ISubscriptionOrder {
    subscriptionId: string;
    priceId: string;
}

export class UpdateAvatarImageInput implements IUpdateAvatarImageInput {
    imageData!: string | undefined;
    userId!: string;

    constructor(data?: IUpdateAvatarImageInput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.imageData = _data["imageData"];
            this.userId = _data["userId"];
        }
    }

    static fromJS(data: any): UpdateAvatarImageInput {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateAvatarImageInput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["imageData"] = this.imageData;
        data["userId"] = this.userId;
        return data;
    }

    clone(): UpdateAvatarImageInput {
        const json = this.toJSON();
        let result = new UpdateAvatarImageInput();
        result.init(json);
        return result;
    }
}

export interface IUpdateAvatarImageInput {
    imageData: string | undefined;
    userId: string;
}

export class UpdateAvatarImageOutput implements IUpdateAvatarImageOutput {
    readonly hasErrors!: boolean;
    readonly isSuccessful!: boolean;
    readonly errors!: IError[] | undefined;
    readonly friendlyErrorMessage!: string | undefined;
    id!: string | undefined;
    url!: string | undefined;

    constructor(data?: IUpdateAvatarImageOutput) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (<any>this).hasErrors = _data["hasErrors"];
            (<any>this).isSuccessful = _data["isSuccessful"];
            if (Array.isArray(_data["errors"])) {
                (<any>this).errors = [] as any;
                for (let item of _data["errors"])
                    (<any>this).errors!.push(IError.fromJS(item));
            }
            (<any>this).friendlyErrorMessage = _data["friendlyErrorMessage"];
            this.id = _data["id"];
            this.url = _data["url"];
        }
    }

    static fromJS(data: any): UpdateAvatarImageOutput {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateAvatarImageOutput();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["hasErrors"] = this.hasErrors;
        data["isSuccessful"] = this.isSuccessful;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item.toJSON());
        }
        data["friendlyErrorMessage"] = this.friendlyErrorMessage;
        data["id"] = this.id;
        data["url"] = this.url;
        return data;
    }

    clone(): UpdateAvatarImageOutput {
        const json = this.toJSON();
        let result = new UpdateAvatarImageOutput();
        result.init(json);
        return result;
    }
}

export interface IUpdateAvatarImageOutput {
    hasErrors: boolean;
    isSuccessful: boolean;
    errors: IError[] | undefined;
    friendlyErrorMessage: string | undefined;
    id: string | undefined;
    url: string | undefined;
}

export class UserProfileInfo implements IUserProfileInfo {
    id!: string;
    username!: string | undefined;
    fullName!: string | undefined;
    userType!: UserType;

    constructor(data?: IUserProfileInfo) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.username = _data["username"];
            this.fullName = _data["fullName"];
            this.userType = _data["userType"];
        }
    }

    static fromJS(data: any): UserProfileInfo {
        data = typeof data === 'object' ? data : {};
        let result = new UserProfileInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["username"] = this.username;
        data["fullName"] = this.fullName;
        data["userType"] = this.userType;
        return data;
    }

    clone(): UserProfileInfo {
        const json = this.toJSON();
        let result = new UserProfileInfo();
        result.init(json);
        return result;
    }
}

export interface IUserProfileInfo {
    id: string;
    username: string | undefined;
    fullName: string | undefined;
    userType: UserType;
}

export enum UserType {
    Student = "Student",
    Parent = "Parent",
    TeamMember = "TeamMember",
}

export class ApiException extends Error {
    message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}