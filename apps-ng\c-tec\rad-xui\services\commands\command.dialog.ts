import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { injectDialogInstance } from '@tec/rad-core/abstractions';
import { RadFormLayout } from '@tec/rad-xui/form';
import { RadDialogLayout } from '@tec/rad-ui/layout';
import { CommandHandler } from './command.types';
import { apiResultFrom } from '@tec/rad-core/utils';


export interface CommandData<T,R> {
    title: string,
    fields: FormlyFieldConfig[],
    cmd: T,
    handler: CommandHandler<T,R>
}


@Component({
    selector: 'rad-exp-command',
    templateUrl: './command.dialog.html',
    styleUrls: ['./command.dialog.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        RadDialogLayout,
        RadFormLayout,
        //Add required imports here
    ]
})
export class CommandDialog {
        

    dialog = injectDialogInstance<CommandData<unknown, unknown>>();
    data = this.dialog.data;
    @ViewChild(RadFormLayout)
    form: RadFormLayout;

    title = this.data.title;


    protected cancel() { 
        this.dialog.cancel();
    }

    protected async submit() {

        this.markAllFieldsAsTouched(this.form.form());
        if (!this.form.form().valid) {
            return;
        }

        const response = await apiResultFrom(this.data.handler(this.data.cmd));

        if(response.isError){
            this.dialog.cancel();
        }
        

        this.dialog.confirm(response.value);
    }

    markAllFieldsAsTouched(form: FormGroup) {
        Object.keys(form.controls).forEach(field => {
          const control = form.get(field);
          if (control) {
            control.markAsTouched({ onlySelf: true });
          }
        });
      }

    
        
}