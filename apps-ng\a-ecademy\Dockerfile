# Stage 1: Compile and Build angular codebase

# Use official node image as the base image
FROM node:lts as source
#FROM node:16.13.0 as source

# Set the working directory
WORKDIR /app

# Add the source code to app
COPY . .


RUN npm install -g npm@latest

# Install all the dependencies
RUN npm install --legacy-peer-deps

# Generate the build of the sirus-app
RUN npm run build:ed-admin


# Stage 2: Serve app with nginx server
FROM nginx:latest


# use custom nginx conf for angular
COPY a-ecademy/nginx.conf /etc/nginx/nginx.conf

# Copy the build output to replace the default nginx contents.
COPY --from=source  app/dist/a-ecademy/x-admin-app/browser wwwroot/admin
#COPY --from=source  /app/dist/libs/ usr/share/nginx/html/

EXPOSE 8080

# When the container starts, replace the env.js with values from environment variables
CMD ["/bin/sh",  "-c",  "envsubst < /wwwroot/admin/assets/appconfig.template.json > /wwwroot/admin/assets/appconfig.json && exec nginx -g 'daemon off;'"]

