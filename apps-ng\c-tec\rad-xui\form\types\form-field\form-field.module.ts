import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormlyModule } from '@ngx-formly/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RadFormlyField } from './form-field.wrapper';
import { RadFormItemComponent } from './form-item.component';
import { InfoBlock } from './info-block';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DatePipe,
    InfoBlock,

    FormlyModule.forChild({
      wrappers: [
        {
          name: 'rad-form-field',
          component: RadFormItemComponent,
        },
      ],
    }),
  ],
})
export class RadFormlyFieldModule {}
