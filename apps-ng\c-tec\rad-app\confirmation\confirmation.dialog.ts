import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from '@angular/cdk/dialog';
import { DialogResult, injectDialogData, injectDialogRef } from '@tec/rad-core/abstractions';
import { ConfirmationConfig } from '@tec/rad-core/abstractions';
import { ButtonModule } from 'primeng/button';
import { RadDialogLayout } from '@tec/rad-ui/layout';

@Component({
    selector: 'rad-confirmation-dialog',
    templateUrl: './confirmation.dialog.html',
    styleUrls: ['./confirmation.dialog.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [
        ButtonModule,
        DialogModule,
        CommonModule,
        RadDialogLayout
    ]
})
export class ConfirmationDialog 
{
    /**
     * Constructor
     */

    data = injectDialogData<ConfirmationConfig>();

    dialog = injectDialogRef();

    get contextColor(){

        if(this.data?.severity === undefined){
            return 'context-primary';
        }

        return `context-${this.data.severity}`;
    }

    


    cancel(): void {
        this.dialog.close(DialogResult.cancel());
    }

    confirm(): void {
        this.dialog.close(DialogResult.complete(true));
    }

}
