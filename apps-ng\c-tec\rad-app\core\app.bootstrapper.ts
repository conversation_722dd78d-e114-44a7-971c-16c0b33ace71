import {
  BootstrapOptions,
  CompilerOptions,
  enableProdMode,
  Type,
} from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { APP_CONFIG } from '@tec/rad-core/abstractions';

export class RadAppBootstrapper {
  static bootstrap<M>(
    moduleType: Type<M>,
    compilerOptions?:
      | (CompilerOptions & BootstrapOptions)
      | Array<CompilerOptions & BootstrapOptions>
  ) {
    fetch('assets/appconfig.json')
      .then((res) => res.json())
      .then((config) => {
        if (config.environment?.production) {
          enableProdMode();
        }

        platformBrowserDynamic([
          { provide: APP_CONFIG, useValue: config, multi: true },
        ])
          .bootstrapModule(moduleType, compilerOptions)
          .catch((err) => console.error(err));
      });
  }
}
