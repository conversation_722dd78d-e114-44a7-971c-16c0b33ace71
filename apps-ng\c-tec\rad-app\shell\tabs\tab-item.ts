
import { Subject, Subscription } from 'rxjs';

import { TabInfo } from './tab-info';
import { UrlHelper } from '@tec/rad-core/utils';
import { Params } from '@angular/router';
import { TabConfig } from './tab-config';
import { ITabItem } from '@tec/rad-core/abstractions';




export class TabItem implements ITabItem {

    id: string;
    private _rootUrl = '';
    get rootUrl() {
        return this._rootUrl; 
    }
    set rootUrl(value:string) {
        this._rootUrl = UrlHelper.normalize(value)
    }


    private _queryParams: Params;

    get queryParams() {
        return this._queryParams;
    }


    private _currentUrl = '';


    
    get currentUrl() {
        return this._currentUrl;
    }

    private set currentUrl(value:string) {
        this._currentUrl = UrlHelper.normalize(value);
    }


    private _linkUrl:string|undefined = undefined;
    get linkUrl() {
        return this._linkUrl;
    }
    
    title= '';
    subtitle= '';
    isDefault = false;

    private _isActive= false;

    get isActive() {
        return this._isActive;
    }

    path = "";
    pathKey = "";

    private closedSource = new Subject<any>();
    get closed$() {
        return this.closedSource.asObservable();
    }

    private activatedSource = new Subject<any>();
    get activated$() {
        return this.activatedSource.asObservable();
    }

    titleChangedSource = new Subject<any>();
    get titleChanged$() {
        return this.titleChangedSource.asObservable();
    }
    
    subscription: Subscription;

    isRootOf(url: string): boolean{
        if(this.isDefault) {
            return UrlHelper.normalize(this.currentUrl) == UrlHelper.normalize(url);
        }
        const normalizedUrl = UrlHelper.normalize(url);
        const normRootUrl = UrlHelper.normalize(this.rootUrl);
        const compare = normalizedUrl.startsWith(normRootUrl)
        return compare ;
        

    }

    setTitle(title: string, subTitle?: string): void {
        this.subtitle = '';
        this.title = title;
        if(subTitle) {
            this.subtitle = subTitle;
        }
        this.titleChangedSource.next(null);
    }

    updateUrlConfig(config: TabConfig) {
        console.log(`Change tab url from  ${this.currentUrl} to ${config.currentUrl}`)
        this.currentUrl = UrlHelper.normalize(config.currentUrl);
        this._queryParams = config.queryParams;
        if(this.isDefault) {
            this.rootUrl = UrlHelper.normalize(config.currentUrl);
        }
    }

    updateCurrentUrl(url:string) {
        this._currentUrl = url;
        if(this.isDefault) {
            this.rootUrl = UrlHelper.normalize(url);
        }
    }

    updateLinkUrl(url:string) {
        this._linkUrl = UrlHelper.normalize(url);
    }

    close() {
        this.closedSource.next(null);
    }

    activate(activate = true) {
        this._isActive = activate;

        if(activate) {
            this.activatedSource.next(null);
        }

       
    }




}
