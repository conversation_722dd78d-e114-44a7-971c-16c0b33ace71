// result.types.ts

import { firstValueFrom, Observable } from "rxjs";

/**
 * Represents an error with a code, message, and optional metadata
 */
export interface ResultError {
  code: string;
  message: string;
  type: ErrorType;
  metadata?: Record<string, any>;
}

/**
 * Error type classification
 */
export enum ErrorType {
  Failure = 'Failure',
  Unexpected = 'Unexpected',
  Validation = 'Validation',
  Conflict = 'Conflict',
  NotFound = 'NotFound',
  Unauthorized = 'Unauthorized',
  Forbidden = 'Forbidden',
  BadRequest = 'BadRequest',
  Timeout = 'Timeout',
  ServiceUnavailable = 'ServiceUnavailable'
}

// result.ts
/**
 * Result<T> class - A discriminated union type for handling success/failure cases
 * Similar to ErrorOr pattern but adapted for TypeScript/Angular
 */
export class Result<T = void> {
  private constructor(
    private readonly _value?: T,
    private readonly _errors?: ResultError[]
  ) {}

  /**
   * Creates a successful result
   */
  static ok<T = void>(value?: T): Result<T> {
    return new Result<T>(value, undefined);
  }

  /**
   * Creates a failed result with one or more errors
   */
/**
   * Creates a failed result with one or more errors
   */
  static fail<T = void>(
    error: ResultError | ResultError[] | string, 
    errorType: ErrorType = ErrorType.Unexpected
  ): Result<T> {
    let errors: ResultError[];
    
    if (typeof error === 'string') {
      errors = [{
        code: errorType.toUpperCase(),
        message: error,
        type: errorType
      }];
    } else if (Array.isArray(error)) {
      errors = error;
    } else {
      errors = [error];
    }
    
    return new Result<T>(undefined, errors);
  }

  /**
   * Creates a failed result from an Error object
   */
  static fromError<T = void>(error: Error, type: ErrorType = ErrorType.Unexpected): Result<T> {
    return Result.fail<T>({
      code: error.name || 'UNKNOWN_ERROR',
      message: error.message,
      type,
      metadata: { stack: error.stack }
    });
  }

  /**
   * Creates a failed result from an HTTP error response
   */
  static fromHttpError<T = void>(status: number, message: string, metadata?: any): Result<T> {
    const errorMap: Record<number, ErrorType> = {
      400: ErrorType.BadRequest,
      401: ErrorType.Unauthorized,
      403: ErrorType.Forbidden,
      404: ErrorType.NotFound,
      409: ErrorType.Conflict,
      408: ErrorType.Timeout,
      503: ErrorType.ServiceUnavailable
    };

    return Result.fail<T>({
      code: `HTTP_${status}`,
      message,
      type: errorMap[status] || ErrorType.Unexpected,
      metadata
    });
  }

  /**
   * Checks if the result is successful
   */
  get isSuccess(): boolean {
    return this._errors === undefined;
  }

  /**
   * Checks if the result is a failure
   */
  get isFailure(): boolean {
    return !this.isSuccess;
  }

  /**
   * Gets the value (throws if result is failure)
   */
  get value(): T {
    if (this.isFailure) {
      throw new Error('Cannot get value from a failed result. Check isSuccess first.');
    }
    return this._value as T;
  }

  /**
   * Gets the value or returns a default
   */
  getValueOrDefault(defaultValue: T): T {
    return this.isSuccess ? (this._value as T) : defaultValue;
  }

  /**
   * Gets the value or null
   */
  getValueOrNull(): T | null {
    return this.isSuccess ? (this._value as T) : null;
  }

  /**
   * Gets the errors (throws if result is success)
   */
  get errors(): ResultError[] {
    if (this.isSuccess) {
      throw new Error('Cannot get errors from a successful result.');
    }
    return this._errors as ResultError[];
  }

  /**
   * Gets the first error or null
   */
  get firstError(): ResultError | null {
    return this.isFailure && this._errors ? this._errors[0] : null;
  }

  /**
   * Gets errors of a specific type
   */
  getErrorsOfType(type: ErrorType): ResultError[] {
    return this.isFailure ? this.errors.filter(e => e.type === type) : [];
  }

  /**
   * Checks if result has a specific error type
   */
  hasErrorType(type: ErrorType): boolean {
    return this.isFailure && this.errors.some(e => e.type === type);
  }

  /**
   * Maps the value if successful
   */
  map<U>(fn: (value: T) => U): Result<U> {
    return this.isSuccess 
      ? Result.ok(fn(this._value as T))
      : Result.fail<U>(this._errors as ResultError[]);
  }

  /**
   * FlatMap (bind) - chains operations that return Results
   */
  flatMap<U>(fn: (value: T) => Result<U>): Result<U> {
    return this.isSuccess 
      ? fn(this._value as T)
      : Result.fail<U>(this._errors as ResultError[]);
  }

  /**
   * Async flatMap for promise-based operations
   */
  async flatMapAsync<U>(fn: (value: T) => Promise<Result<U>>): Promise<Result<U>> {
    return this.isSuccess 
      ? await fn(this._value as T)
      : Result.fail<U>(this._errors as ResultError[]);
  }

  /**
   * Match pattern - handles both success and failure cases
   */
  match<U>(options: {
    success: (value: T) => U;
    failure: (errors: ResultError[]) => U;
  }): U {
    return this.isSuccess 
      ? options.success(this._value as T)
      : options.failure(this._errors as ResultError[]);
  }

  /**
   * Async match pattern
   */
  async matchAsync<U>(options: {
    success: (value: T) => Promise<U>;
    failure: (errors: ResultError[]) => Promise<U>;
  }): Promise<U> {
    return this.isSuccess 
      ? await options.success(this._value as T)
      : await options.failure(this._errors as ResultError[]);
  }

  /**
   * Tap - performs side effects without changing the result
   */
  tap(fn: (value: T) => void): Result<T> {
    if (this.isSuccess) {
      fn(this._value as T);
    }
    return this;
  }

  /**
   * TapError - performs side effects on errors
   */
  tapError(fn: (errors: ResultError[]) => void): Result<T> {
    if (this.isFailure) {
      fn(this._errors as ResultError[]);
    }
    return this;
  }

  /**
   * Converts to a Promise
   */
  toPromise(): Promise<T> {
    return this.isSuccess 
      ? Promise.resolve(this._value as T)
      : Promise.reject(this._errors);
  }

  /**
   * Combines multiple results (all must succeed)
   */
  static combine<T extends readonly Result<any>[]>(
    results: T
  ): Result<{ [K in keyof T]: T[K] extends Result<infer U> ? U : never }> {
    const errors: ResultError[] = [];
    const values: any[] = [];

    for (const result of results) {
      if (result.isFailure) {
        errors.push(...result.errors);
      } else {
        values.push(result.value);
      }
    }

    return errors.length > 0 
      ? Result.fail(errors)
      : Result.ok(values as any);
  }

  /**
   * Try-catch wrapper for async operations
   */
  static async tryAsync<T>(
    fn: () => Promise<T>,
    errorType: ErrorType = ErrorType.Unexpected
  ): Promise<Result<T>> {
    try {
      const value = await fn();
      return Result.ok(value);
    } catch (error) {
      return Result.fromError(error as Error, errorType);
    }
  }

  /**
   * Try-catch wrapper for async operations
   */
  static async try$<T>(
    observable: Observable<T>,
    errorType: ErrorType = ErrorType.Unexpected
  ): Promise<Result<T>> {
    return this.tryAsync(async () => await firstValueFrom(observable));
  }

  /**
   * Try-catch wrapper for sync operations
   */
  static try<T>(
    fn: () => T,
    errorType: ErrorType = ErrorType.Unexpected
  ): Result<T> {
    try {
      const value = fn();
      return Result.ok(value);
    } catch (error) {
      return Result.fromError(error as Error, errorType);
    }
  }
}

// result.validators.ts
/**
 * Validation helper functions for common scenarios
 */
export class ResultValidators {
  /**
   * Validates that a value is not null or undefined
   */
  static required<T>(value: T | null | undefined, fieldName: string): Result<T> {
    if (value === null || value === undefined) {
      return Result.fail<T>({
        code: 'REQUIRED_FIELD',
        message: `${fieldName} is required`,
        type: ErrorType.Validation,
        metadata: { field: fieldName }
      });
    }
    return Result.ok(value);
  }

  /**
   * Validates string length
   */
  static stringLength(
    value: string,
    fieldName: string,
    min?: number,
    max?: number
  ): Result<string> {
    if (min !== undefined && value.length < min) {
      return Result.fail<string>({
        code: 'STRING_TOO_SHORT',
        message: `${fieldName} must be at least ${min} characters`,
        type: ErrorType.Validation,
        metadata: { field: fieldName, min, actual: value.length }
      });
    }

    if (max !== undefined && value.length > max) {
      return Result.fail<string>({
        code: 'STRING_TOO_LONG',
        message: `${fieldName} must be at most ${max} characters`,
        type: ErrorType.Validation,
        metadata: { field: fieldName, max, actual: value.length }
      });
    }

    return Result.ok(value);
  }

  /**
   * Validates email format
   */
  static email(value: string): Result<string> {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return Result.fail<string>({
        code: 'INVALID_EMAIL',
        message: 'Invalid email format',
        type: ErrorType.Validation,
        metadata: { value }
      });
    }
    return Result.ok(value);
  }

  /**
   * Combines multiple validation results
   */
  static validate<T>(value: T, ...validators: ((val: T) => Result<any>)[]): Result<T> {
    const errors: ResultError[] = [];

    for (const validator of validators) {
      const result = validator(value);
      if (result.isFailure) {
        errors.push(...result.errors);
      }
    }

    return errors.length > 0 ? Result.fail(errors) : Result.ok(value);
  }
}





