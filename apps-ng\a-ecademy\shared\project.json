{"name": "ed-shared-data", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/shared/data/src", "prefix": "lib", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/shared/data/ng-package.json", "tsConfig": "a-ecademy/shared/data/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/shared/data/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "a-ecademy/shared/data/jest.config.ts", "tsConfig": "a-ecademy/shared/data/tsconfig.spec.json"}}, "lint": {"executor": "@nx/eslint:lint"}}}