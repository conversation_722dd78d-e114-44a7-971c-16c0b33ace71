import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { injectRouteParam } from '@tec/rad-core/utils';
import { CourseService } from '../academy.service';
import { Course } from '../academy.types';
import { RadCard, RadPage } from '@tec/rad-ui/layout';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { RadExpressForm, RadFormConfig, FormLayout } from '@tec/rad-xui/form';
import { ButtonModule } from 'primeng/button';
import { injectTabReference } from '@tec/rad-core/abstractions';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

        
@Component({
    selector: 'app-course-detail',
    templateUrl: './course-detail.page.html',
    styleUrls: ['./course-detail.page.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        RadPage,
        RadCard,
        RadExpressForm,
        ButtonModule,
        FormsModule,
        MatFormFieldModule,
        MatInputModule
        //Add required imports here
    ]
})
export class CourseDetailPage implements OnInit {
    
        

    #service = inject(CourseService);
    #id = injectRouteParam('courseId');
    #tabRef = injectTabReference();

    course = signal<Course>(undefined);

    fields: FormLayout = [
        
        {
            row:[
            { key: 'name', type: 'input'},
            { key: 'startDate', type: 'datepicker',  props: { dateFormat: 'dd-M-yy' } },
            ]
        },
        {
            row:[
            { key: 'category', type: 'select',  
                values: this.#service.getCategories()},
            { key: 'instructor', type: 'select',
                values: this.#service.getTeacherItems()},
            { key: 'isActive',  type: 'checkbox', label:'Active' },
            ]
        },
        { key: 'description', label: 'Description', type: 'textarea'},

        
        
        

        
    ]

    

    ngOnInit(): void {
        const course = this.#service.getCourse(this.#id);
        //his.#tabRef.setTitle('Course', course.name);
        this.course.set(course)
    }
        

        
}