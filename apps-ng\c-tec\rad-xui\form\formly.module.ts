import { NgModule } from '@angular/core';
import { FormlyModule } from '@ngx-formly/core';
import { RadFormlyCheckboxModule } from './types/checkbox/public_api'
import { RadFormlyRadioModule } from './types/radio/public_api';
import { RadFormlyTextAreaModule } from './types/textarea/public_api';
import { RadFormlyInputModule } from './types/input/public_api';
import { RadFormlyFieldModule } from './types/form-field/public_api';
import { RadFormlySelectModule } from './types/select/public_api';
import { RadFormlyDatepickerModule } from './types/datepicker/datepicker.module';
import { RadFormlyMultiSelectModule } from './types/multiselect/multiselect.module';
import { RadFormlyPasswordModule } from './types/password/password.module';
import { RadFormlyDatetimeModule } from './types/datetime/datetime.module';
import { RadFormlyTreeSelectModule } from './types/treeselect/treeselect.module';
import { RadFormlyLayoutModule } from './types/layout/layout-module';


@NgModule({
  imports: [
    RadFormlyFieldModule,
    RadFormlyInputModule,
    RadFormlyPasswordModule,
    RadFormlyTextAreaModule,
    RadFormlyRadioModule,
    RadFormlyCheckboxModule,
    RadFormlySelectModule,
    RadFormlyMultiSelectModule,
    RadFormlyTreeSelectModule,
    RadFormlyDatepickerModule,
    RadFormlyDatetimeModule,
    RadFormlyLayoutModule,
    FormlyModule.forRoot({

      validationMessages: [
        { name: 'required', message: 'This field is required' }
      ],
    }),
    //MatNativeDateModule,
    //FormlyMatDatepickerModule
  ],
})
export class RadFormlyModule {}
