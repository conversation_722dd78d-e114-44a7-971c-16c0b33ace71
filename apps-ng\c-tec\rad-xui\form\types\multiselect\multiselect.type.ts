import { Component, ChangeDetectionStrategy, Type, viewChild } from '@angular/core';
import { FieldType, FieldTypeConfig, FormlyFieldConfig, FormlyModule } from '@ngx-formly/core';
import { FormlyFieldProps, FormlyFormFieldModule } from '@ngx-formly/primeng/form-field';
import { FormlyFieldSelectProps } from '@ngx-formly/core/select';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FluidModule } from 'primeng/fluid';
import { Select } from 'primeng/select';
import { MultiSelectModule } from 'primeng/multiselect';
import { FormlySelectModule as FormlyCoreSelectModule } from '@ngx-formly/core/select';
import { ButtonModule } from 'primeng/button';
import { Observable } from 'rxjs';
import { RadFormFieldProps } from '../../form-types';


type OptionsType = any[] | Observable<any[]>;

interface MultiSelectProps extends RadFormFieldProps, FormlyFieldSelectProps {
  appendTo?: any;
  filter?: boolean;
  add?: (field: FormlyFieldConfig<MultiSelectProps>)=>any;
  addIcon?: string;
  addLabel?: string;
  showToggleAll?: boolean;
  showClear?: boolean;
  display?: string;

}

export interface FormlyMultiSelectFieldConfig extends FormlyFieldConfig<MultiSelectProps> {
  type: 'multiselect' | Type<RadFormlyMultiSelect>;
}

@Component({
    selector: 'rad-formly-multiselect',
    template: `
    <!-- <p-multiselect [options]="cities" [(ngModel)]="selectedCities" optionLabel="name" placeholder="Select Cities" [maxSelectedLabels]="3" styleClass="w-full md:w-80" /> -->
    <p-multiselect
      [placeholder]="props.placeholder"
      [options]="props.options | formlySelectOptions : field | async"
      [formControl]="formControl"
      [formlyAttributes]="field"
      [showClear]="!props.required || props.showClear"
      [showToggleAll]="props.showToggleAll??false"
      [filter]="props.filter"
      (onChange)="props.change && props.change(field, $event)"
      [display]="props.display || 'comma'"
      
      appendTo="body" fluid
    >
    <!-- Optional footer button -->
    <ng-template pTemplate="footer" *ngIf="props.add">
        <div class="w-full flex flex-row px-2 py-2 justify-end bg-gray-50 border-t-gray-100 rounded-br-md rounded-bl-mds">
          <button type="button"
                  pButton 
                  [icon]="props.addIcon || 'pi pi-plus'"
                  [label]="props.addLabel || 'Add'"
                  (click)="addOption()">
          </button>
        </div>
      </ng-template>
    </p-multiselect>
  `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports:[
      CommonModule,
      ReactiveFormsModule,
      MultiSelectModule,
      FluidModule,
      FormlyFormFieldModule,
      FormlyCoreSelectModule,
      FormlyModule,
      ButtonModule
    ]
    
})
export class RadFormlyMultiSelect extends FieldType<FieldTypeConfig<MultiSelectProps>> {

  private control = viewChild(Select)

  async addOption() {

    if(!this.props.add) {
      return;
    }
    this.control().hide();
    this.props.add(this.field);
    
    
    
  }

  

}
