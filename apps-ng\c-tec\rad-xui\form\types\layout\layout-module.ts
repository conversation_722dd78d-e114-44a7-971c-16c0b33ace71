import { NgModule } from '@angular/core';
import { FormlyModule } from '@ngx-formly/core';
import { SeparatorTypeComponent } from './separator';
import { DividerTypeComponent } from './divider';

@NgModule({
  declarations: [],
  imports: [
    FormlyModule.forRoot({

      validationMessages: [
        { name: 'required', message: 'This field is required' }
      ],
      types: [
        { name: 'separator', component: SeparatorTypeComponent, wrappers: [] },
        { name: 'divider', component: DividerTypeComponent, wrappers: [] },
      ],
    }),
  ],
})
export class RadFormlyLayoutModule {}