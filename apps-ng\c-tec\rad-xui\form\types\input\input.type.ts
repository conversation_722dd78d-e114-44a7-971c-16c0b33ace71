import { Component, ChangeDetectionStrategy, Type } from '@angular/core';
import { FieldType, FieldTypeConfig, FormlyFieldConfig } from '@ngx-formly/core';
import { FormlyFieldProps } from '@ngx-formly/primeng/form-field';
import { RadFormFieldProps } from '../../form-types';


interface InputProps extends RadFormFieldProps {}

export interface FormlyInputFieldConfig extends FormlyFieldConfig<InputProps> {
  type: 'input' | Type<RadFormlyInput>;
}

@Component({
    selector: 'rad-formly-input',
    template: `
    <input
      *ngIf="props.type !== 'number'; else numberTmp"
      pInputText
      [type]="props.type || 'text'"
      [formControl]="formControl"
      [formlyAttributes]="field" 
      fluid
    />
    <ng-template #numberTmp>
      <input type="number" pInputText [formControl]="formControl" [formlyAttributes]="field" />
    </ng-template>
  `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class RadFormlyInput extends FieldType<FieldTypeConfig<InputProps>> {}
