import {Component, OnDestroy, OnInit, signal, ViewEncapsulation} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { Router, RouterModule } from '@angular/router';


import { Subject, timer } from 'rxjs';
import { finalize, takeUntil, takeWhile, tap } from 'rxjs/operators';
import { AuthenticationManager } from '@tec/rad-core/abstractions';
import { CommonModule } from '@angular/common';


@Component({
    selector: 'rad-account-logout',
    templateUrl: './logout.page.html',
    encapsulation: ViewEncapsulation.None,
    imports: [CommonModule, MatButtonModule, RouterModule]
})
export class LogoutPage implements OnInit, OnDestroy {
  countdown = signal(5);
  countdownMapping: any = {
    '=1': '# second',
    other: '# seconds',
  };
  private _unsubscribeAll: Subject<any> = new Subject<any>();

  /**
   * Constructor
   */
  constructor(private _authService: AuthenticationManager, private _router: Router) {}

  // -----------------------------------------------------------------------------------------------------
  // @ Lifecycle hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    // Sign out
    this._authService.logout();

    // Redirect after the countdown
    timer(1000, 1000)
      .pipe(
        finalize(() => {
          this._router.navigate(['account', 'login']);
        }),
        takeWhile(() => this.countdown() > 0),
        takeUntil(this._unsubscribeAll),
        tap(() => this.countdown.set(this.countdown()-1))
      )
      .subscribe();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
}
