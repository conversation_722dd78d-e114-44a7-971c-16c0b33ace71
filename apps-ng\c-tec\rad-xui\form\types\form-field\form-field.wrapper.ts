import { Component, computed, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { FieldWrapper, FormlyFieldConfig, FormlyFieldProps as CoreFormlyFieldProps } from '@ngx-formly/core';
import { RadFormFieldProps } from '../../form-types';
import { isObservable, Subscription } from 'rxjs';
import { compileClasses } from '@tec/rad-ui/common';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { InfoBlock } from './info-block';

@Component({
  selector: 'rad-formly-field',
  imports: [CommonModule, ReactiveFormsModule, DatePipe, InfoBlock],
  template: `
    <div class="rad-field p-field" [class]="fieldClasses()">
      @if (layout === 'horizontal') {
        <div class="label-wrapper" [style.width]="props.labelWidth || '120px'">
          <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
            {{ props.label }}
            <span class='field-required' *ngIf="props.required && props.hideRequiredMarker !== true" aria-hidden="true">*</span>
          </label>
        </div>
        <div class="content-wrapper">
        <div class="component-wrapper">
          @if (infoMode) {
            <rad-info-block [value]="displayValue()" [options]="props.info"></rad-info-block>
            <!-- <div class="formly-info-value">{{ displayValue() }}</div> -->
          } @else {
            <ng-container #fieldComponent></ng-container>
          }
        </div>
        @if(!infoMode){
        <div class="error-wrapper" style="width:100%;">
          <small *ngIf="showError" class="field-error">
            <formly-validation-message class="ui-message-text" [field]="field"></formly-validation-message>
          </small>
        </div>

        }
        </div>
      } @else {
        <div class="label-wrapper">
          <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
            {{ props.label }}
            <span class='field-required' *ngIf="props.required && props.hideRequiredMarker !== true" aria-hidden="true">*</span>
          </label>
        </div>
        <div class="content-wrapper">
        <div class="component-wrapper">
          @if (infoMode) {
            <rad-info-block [value]="displayValue()" [options]="props.info"></rad-info-block>
            <!-- <div class="formly-info-value">{{ displayValue() }}</div> -->
          } @else {
            <ng-container #fieldComponent></ng-container>
          }
        </div>
        @if(!infoMode){
        <div class="error-wrapper">
          <small *ngIf="showError" class="field-error">
            <formly-validation-message class="ui-message-text" [field]="field"></formly-validation-message>
          </small>
        </div>
        }
        </div>
      }
    </div>
  `
})
export class RadFormlyField extends FieldWrapper<FormlyFieldConfig<RadFormFieldProps>> implements OnInit, OnDestroy {



  private optionsSub?: Subscription;
  displayValue = signal('');

  get infoMode() {

    // Prefer explicit per-field prop if set, fallback to global formState
    return this.props.infoMode ?? this.options.formState?.infoMode;
  }

  get layout() {

    
    return (this.props.layout || this.options.formState?.layout) ?? 'vertical';
  }

  fieldClasses = computed(() => {
    const info = this.props.info;
    return compileClasses([
      
      this.infoMode ? 'rad-field-info' : '',
      this.layout === 'horizontal' ? 'rad-field-horizontal' : 'rad-field-vertical',
     
    ]);
  })

  constructor(private datePipe: DatePipe) {
    super();
  }

ngOnInit() {
  this.setupDisplayValue();
  this.formControl.valueChanges.subscribe(() => this.setupDisplayValue());
}

ngOnDestroy() {
  this.optionsSub?.unsubscribe();
}



  

  setupDisplayValue() {
  const value = this.formControl?.value;


  // 1. If options is Observable, subscribe and update when loaded
  if (isObservable(this.props.options)) {
    this.optionsSub = this.props.options.subscribe((opts: any[]) => {
      const displayLabel = this.resolveDisplayValue(value, opts);
      this.displayValue.set(displayLabel);
    });
    // Optionally show value while loading

    //this.displayValue.set( value ?? '—');
  } 
  // 2. If options is array, resolve immediately
  else if (Array.isArray(this.props.options)) {
    this.displayValue.set(this.resolveDisplayValue(value, this.props.options));
  } 
  // 3. Otherwise, just show value
  else {
    this.displayValue.set(this.resolveDisplayValue(value));
  }
}

// Utility to find the label from options
resolveDisplayValue(value: any, options?: any[],) {
  // If options exist, try to find label
  if (options && value !== undefined && value !== null && value !== '') {
    const match = options.find(o => o.value === value);
    if (match) return match.label;
  }

  // If value is a date (Date instance or ISO string), format it
  if (this.isDate(value)) {
    const dateFormat = this.props.format || 'yyyy-MM-dd';
    return this.datePipe.transform(value, dateFormat) ?? '—';
  }

  // Fallback: show value or "—"
  return (value === null || value === undefined || value === '') ? '—' : value;
}

isDate(val: any): boolean {
  if(this.props.type == 'datepicker' || this.props.type == 'datetime') return true;
  return false
}
  
}
