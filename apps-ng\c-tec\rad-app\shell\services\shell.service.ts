import { Inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { SessionManager } from '@tec/rad-core/abstractions';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';
import { first } from 'rxjs/operators';

import { INavItem } from '@tec/rad-core/abstractions';





@Injectable()
export class RadShellService  {


  private _menuItems: BehaviorSubject<INavItem[]> = new BehaviorSubject<INavItem[]>([]);

  // get menuItems$(): Observable<INavItem[]> {
  //   return this._menuItems.asObservable();
  // }

  constructor(

    protected sessionService: SessionManager
    ) {



   }

   animation = 'animate__fadeInLeft';

  async resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<void> {
      //await this.loadMenu();
      await this.sessionService.init();
  }


  //  async loadMenu(): Promise<void> {
  //    const value = this._menuItems.value;
  //    if(value && value.length > 0) {
  //      return;
  //    }
  //    const items = await firstValueFrom(this._itemsService.getMenuItems());
  //    this._menuItems.next(items);
  //  }

  changeAnimation(type = 'add') {
    if (this.animation) {
        const ele: any = document.querySelector('.animation');
        if (type === 'add') {
            ele?.classList.add('animate__animated');
            ele?.classList.add(this.animation);
        } else {
            ele?.classList.remove('animate__animated');
            ele?.classList.remove(this.animation);
        }
    }
}

}
