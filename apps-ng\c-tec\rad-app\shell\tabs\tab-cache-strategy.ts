import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, DetachedRouteHandle, RouteReuseStrategy } from '@angular/router';
import { KeyedCollection, UrlHelper } from '@tec/rad-core/utils';


import { TabConfigHelper } from './tab-config';
import {TabItem} from "./tab-item";
import { TabManagerService } from './tab-manager.service';

class RouteData {
  
  key = "";
  handle: DetachedRouteHandle;
}
@Injectable()
export class TabCacheReuseStrategy implements RouteReuseStrategy {

  storedRoutes = new KeyedCollection<RouteData>();

  

  tabs: TabItem[] = [];

  constructor(private navigation: TabManagerService) {
    navigation.tabClosed$.subscribe(tab=>{
      this.removeTabRoutes(tab);
    })
    navigation.tabUrlChanging$.subscribe(tab=>{
      //this.removeRoute(tab.currentUrl);
    })
  }

  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
    if (!route.routeConfig) return null;
    if (route.routeConfig.loadChildren || route.routeConfig.children ) return null;
    let path = this.getPath(route);

    const tab = this.navigation.findTabWithUrl(path);

    if(tab === undefined) {
      return null;
    }

    path = path.toLowerCase();
    const item = this.storedRoutes.get(path);
    
    return item.handle;
  }

  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    const path = this.getPath(route);


    const tab = this.navigation.findTabRootForUrl(path);

    

    if(tab === undefined) {
      return false;
    }

    const keyIsPresent = this.storedRoutes.containsKey(path.toLowerCase());
    return keyIsPresent;
  }

  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    
    const path = this.getPath(route);

    const tab = this.navigation.findTabRootForUrl(path);
    
    if(tab) {
      console.log(`Should detach route for path ${path}`)
      return true;
      
    }

    return false;
  }

  shouldReuseRoute(future: ActivatedRouteSnapshot, current: ActivatedRouteSnapshot): boolean {
    const futurePath = this.getPath(future);
    const currentPath = this.getPath(current);
    const futureTab = this.navigation.getTabReference(future);
    const currentTab = this.navigation.getTabReference(current);

    const futureTabId = futureTab?.id;
    const currentTabId = currentTab?.id;


    console.log(`Future path ${futurePath} current path  ${currentPath}`)

    const isSameTab = (futureTabId && currentTabId ) && futureTabId == currentTabId;

    const isSameComponent = future.component == current.component;
    const reuse =  (futurePath === currentPath || isSameTab);
    return reuse; 
  }

  store(route: ActivatedRouteSnapshot, detachedTree: DetachedRouteHandle | null): void {
    const data = new RouteData();
    const config = TabConfigHelper.getConfig(route);
    
    data.key = this.getPath(route);
    
    data.handle = detachedTree;

    if(data.handle === null) {
      return;
    }

    this.storedRoutes.add(data.key.toLowerCase(), data);
  }

  private getPath(route: ActivatedRouteSnapshot): string {
    let url = UrlHelper.getResolvedUrl(route);
    url = UrlHelper.normalize(url);
    return url.toLowerCase();
  }

  

  public removeRoute(path: string) {
    path = UrlHelper.normalize(path).toLowerCase();
    if(this.storedRoutes.containsKey(path)){
      this.storedRoutes.remove(path);
      console.log(`Removing route for path ${path}`)
    }
    
  }

  private removeTabRoutes(tab: TabItem) {
    const tabsToRemove = [];
    for(const item of this.storedRoutes.keys()){
      if(tab.isRootOf(item)) {
        tabsToRemove.push(item)
      }
    }

    for(const key of tabsToRemove) {
      this.storedRoutes.remove(key);
    }

  }

}


