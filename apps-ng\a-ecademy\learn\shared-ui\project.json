{"name": "ed-pep-shared-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/learn/shared-ui/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/learn/shared-ui/ng-package.json", "tsConfig": "a-ecademy/learn/shared-ui/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/learn/shared-ui/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "lint": {"executor": "@nx/eslint:lint"}}}