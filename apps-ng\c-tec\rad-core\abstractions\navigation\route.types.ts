import { InjectionToken, Provider } from '@angular/core';
import { Data, Route } from '@angular/router';





export interface AppRouteData extends Data {
    reuse?: boolean;
    title?: string;
    subtitle?: string;
    tabPage?: boolean;
    tabPath?: string;
    tabPathKey?: string;
    view?: string;
    viewId?: string;
    pathName?: string;
    pathId?: string;


}



export interface AppRoute extends Route {
    data?: AppRouteData;
    name?: string;
    id?: string;
    title?: string;
    key?: string;
    children?:AppRoute[];
    composite?: boolean;
    defaultView?:string;
  }




  export type AppRoutes = AppRoute[];





