import { CommonModule } from '@angular/common';
import {
  Component,
  Inject,
  InjectionToken,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  NgForm,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { tecAnimations } from '@tec/rad-ui/animations';


import { AuthenticationManager, ColorContext } from '@tec/rad-core/abstractions';
import { AppConfigurationService } from '@tec/rad-core/abstractions';

import { RadFormField, RadFormItem } from '@tec/rad-xui/form';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { PasswordModule } from 'primeng/password';

import { ButtonModule } from 'primeng/button';
import { Fluid } from 'primeng/fluid';


@Component({
    selector: 'rad-account-login',
    templateUrl: './login.page.html',
    encapsulation: ViewEncapsulation.None,
    animations: tecAnimations,
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        FormsModule,
        RadFormField,
        ButtonModule,
        InputTextModule,
        PasswordModule,
        CheckboxModule,
        MessageModule,
        Fluid
    ]
})
export class LoginPage implements OnInit {
  @ViewChild('signInNgForm') signInNgForm: NgForm;

  applicationName: string;
  logo: string;

  alert: { type: ColorContext; message: string } = {
    type: 'success',
    message: '',
  };
  messages = [this.alert]
  signInForm: FormGroup;
  showAlert = false;

  /**
   * Constructor
   */
  constructor(
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthenticationManager,
    private _formBuilder: FormBuilder,
    private config: AppConfigurationService,
    private _router: Router
  ) {
    const appConfig = this.config.getConfigSection('application');
    if (appConfig) {
      this.applicationName = appConfig['name'];
      this.logo = appConfig['logo'];
    }
  }



  // -----------------------------------------------------------------------------------------------------
  // @ Lifecycle hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    // Create the form
    this.signInForm = this._formBuilder.group({
      email: ['', [Validators.required]],
      password: ['', Validators.required],
      rememberMe: [''],
    });
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Sign in
   */
  async signIn(): Promise<void> {
    // Return if the form is invalid
    if (this.signInForm.invalid) {
      return;
    }

    // Disable the form
    this.signInForm.disable();

    // Hide the alert
    this.showAlert = false;

    // Sign in
    // Sign in
    // Get the credentials
    const credentials = this.signInForm.value;

    const result = await this._authService.authenticate(credentials);

    if (result.isSuccessful) {
      // Set the redirect url.
      // The '/signed-in-redirect' is a dummy url to catch the request and redirect the user
      // to the correct page after a successful sign in. This way, that url can be set via
      // routing file and we don't have to touch here.
      const redirectURL =
        this._activatedRoute.snapshot.queryParamMap.get('redirectURL') ||
        '/signed-in-redirect';

      // Navigate to the redirect url
      //window.location.href = redirectURL;
      this._router.navigateByUrl(redirectURL);
    } else {
      // Re-enable the form
      this.signInForm.enable();

      // Reset the form
      this.signInNgForm.resetForm();

      // Set the alert
      this.alert = {
        type: 'danger',
        message: 'Wrong email or password',
      };

      // Show the alert
      this.showAlert = true;
    }
  }
}
