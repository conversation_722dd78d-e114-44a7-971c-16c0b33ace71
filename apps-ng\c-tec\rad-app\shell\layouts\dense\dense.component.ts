import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, OnDestroy, OnInit, signal, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Router, RouterModule, RouterOutlet } from '@angular/router';


import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { UserMenuComponent } from '../../components/user-menu.component';
import { InitialData } from '../../model/shell.types';
import { ShellTabBarComponent } from '../../tabs/tab-bar/tab-bar.component';

import { RadDrawerComponent } from '@tec/rad-ui/component';
import { RadFullscreenComponent } from '@tec/rad-ui/component'
import { AppConfigurationService, AppMediaWatcher } from '@tec/rad-core/abstractions';
import { RadNavbarService, RadSidebar } from '@tec/rad-ui/navbar';
import { AppMenuService } from '@tec/rad-app/core';
import { RadShellService } from '../../services/shell.service';

import { RadRouterOutlet } from '@tec/rad-ui/layout';


@Component({
    selector: 'rad-shell-dense-layout',
    templateUrl: './dense.component.html',
    styleUrls: ['./dense.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        RouterModule,
        MatButtonModule,
        MatDividerModule,
        MatIconModule,
        MatMenuModule,
        RadSidebar,
        UserMenuComponent,
        ShellTabBarComponent,
        RadDrawerComponent,
        RadFullscreenComponent,
        RadRouterOutlet
    ]
})
export class DenseLayoutComponent implements OnInit, OnDestroy, AfterViewInit
{
    #menuService = inject(AppMenuService);
    private service = inject(RadShellService);
    private router = inject(Router);
    private route = inject(ActivatedRoute);
    


    menuItems = this.#menuService.menuItems;    
    
    data: InitialData;
    isScreenSmall = signal(false);
    navigationAppearance = signal<'default' | 'dense'>('default');
    sideBarMode = signal<'over'|'side'>('side');
    title = "Rad";
    applicationName: string;
    logo: string;

    private _unsubscribeAll: Subject<any> = new Subject<any>();

    

    /**
     * Constructor
     */
    constructor(
        private _changeDetector: ChangeDetectorRef,
       
        private _router: Router,
        private _mediaWatcherService: AppMediaWatcher,
        private _navbarService: RadNavbarService,
        private _menuService: AppMenuService,
        private configService: AppConfigurationService
    )
    {
        const appConfig = this.configService.getConfigSection('application');
        if (appConfig) {
          this.applicationName = appConfig['name'];
          this.logo = appConfig['logo'];
        }
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for current year
     */
    get currentYear(): number
    {
        return new Date().getFullYear();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void
    {
        const route = this.route.snapshot.url;
        
        // Subscribe to the resolved route data
        // this._activatedRoute.data.subscribe((data: Data) => {
        //     this.data = data.initialData;
        // });

        // Subscribe to media changes
        this._mediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({matchingAliases}) => {

                // Check if the screen is small
                this.isScreenSmall.set(!matchingAliases.includes('md'));

                // Change the navigation appearance
                this.navigationAppearance.set(this.isScreenSmall() ? 'default' : 'default');
            });

        // this._shellService.menuItems$.pipe(untilDestroyed(this))
        // .subscribe(items=>{
        //     this.menuItems = items;
        //     this._changeDetector.markForCheck();
        // })


    }

    ngAfterViewInit(): void {
        
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void
    {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }



    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Toggle navigation
     *
     * @param name
     */
    toggleNavigation(name: string): void
    {
        // Get the navigation
        const navigation = this._navbarService.getComponent<RadSidebar>(name);

        if ( navigation )
        {
            // Toggle the opened status
            navigation.toggle();
        }
    }

    /**
     * Toggle the navigation appearance
     */
    toggleNavigationAppearance(): void
    {
        const appearance = this.navigationAppearance();
        this.navigationAppearance.set(appearance === 'default' ? 'dense' : 'default');
    }

    

    
    isExpanded = computed(() => this.navigationAppearance() === 'default');

    
}
