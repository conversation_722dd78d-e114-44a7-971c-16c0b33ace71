import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input } from '@angular/core';
import { RadDialogLayout } from '@tec/rad-ui/layout';
import { CreateCourseCommand } from '@ed/shared/data-academics';
import { RadEditForm, RadFormConfig, RadFormLayout } from '@tec/rad-xui/form';
import { injectViewContext } from '@tec/rad-core/composition';
import { RadLookupService } from '@tec/rad-xui/flex';
import { CourseFacade, CreateCourseAction } from '../core';


        
@Component({
    selector: 'ed-create-course-view',
    template: `
    <rad-dialog-layout title="Create Course" (confirm)="createCourse()">
        <rad-form-layout [form]="form" [items]="fields" [model]="cmd()"  />
    </rad-dialog-layout>
    `,
    styles: [],
    imports:[
        CommonModule,
        RadDialogLayout,
        RadFormLayout

    ],
    
})
export class CourseCreateView {
        

    protected context = injectViewContext();
    protected facade = inject(CourseFacade);
    protected lookup = inject(RadLookupService);

        
    protected cmd = signal(new CreateCourseAction());

    protected form = new RadEditForm(this.cmd, signal(true));

    fields: RadFormConfig = [
        { key: 'name', label: 'Name', type: 'input', rowNum: 1, required: true},
        { key: 'description', label: 'Description', type: 'textarea', rowNum: 2},
        { key: 'gradeId', label: 'Grade', type: 'select', rowNum: 3, required: true, values: this.lookup.getLookup$('GradeLevel')}
    ]


    createCourse() {

        if(!this.form.validate()){
            return;
        }
        const command = this.form.model();

        this.facade.createCourse(command);
        this.context.close();
    }
        
}