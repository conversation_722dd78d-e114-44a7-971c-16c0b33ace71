import {Dialog, DialogRef} from '@angular/cdk/dialog';
import {GlobalPositionStrategy} from '@angular/cdk/overlay';
import {ComponentType} from '@angular/cdk/portal';
import {Inject, Injectable, NgZone, Optional, TemplateRef} from '@angular/core';
import {DialogMetadata, DialogResult, IDialogConfig} from '@tec/rad-core/abstractions';
import {firstValueFrom, Observable, of} from 'rxjs';
import {mergeMap} from 'rxjs/operators';
import {DialogAnimationOption, RadDialogConfig} from './dialog.model';


const directionMap = { left: 'left', right: 'left', top: 'top', bottom: 'top' };
const multyMap = { left: 1, right: -1, top: 1, bottom: -1 };

@Injectable({providedIn:'root'})
export class CdkDialogManager {

  private idCounter = 0;
  constructor(public dialog: Dialog,
    private ngZone: NgZone,

    @Optional() @Inject('INCOMING_OPTION') private incomingOptions?: DialogAnimationOption,
    @Optional() @Inject('OUTGOING_OPTION') private outgoingOptions?: DialogAnimationOption
  ) { }



  async showAsync<R = any, T = any>(componentRef: ComponentType<T> | TemplateRef<T>, data?: any, options?: Partial<RadDialogConfig>): Promise<DialogResult<R>> {

    const metaConfig = DialogMetadata.getConfig(componentRef);
    const config: RadDialogConfig = {...this.defaultConfig, ...metaConfig, ...options};

    this.configureSize(config);
    this.configureAnimation(config);

    if (data) {
      config.data = data;
    }



    if (config.modal) {
      config.disableClose = true;
      config.hasBackdrop = true;
    }

    const dialogRef = this.showAnimated(componentRef, config);




    const result = await firstValueFrom(dialogRef.closed);
    return this.getDialogResult<R>(result);

  }




  private getDialogResult<T>(value: any): DialogResult<T> {


    if(value !== undefined && value !== null && value instanceof DialogResult){
      return value;
    }

    const result = new DialogResult<T>();

    if (value?.isCompleted) {
      result.completed = value.isCompleted;
      result.value = value.value;
      return result;
    }

    if (value) {
      result.completed = true;
      result.value = value;
    }

    result.completed = false;
    result.value = value;

    return result;

  }

  private configureSize(config: RadDialogConfig) {

    if(config.width){
      return;
    }

  
    config.maxHeight = '95%';
    switch (config.size) {
      case 'small':
        config.width = '560px';

        config.height = 'auto';
        config.maxHeight ='95%';
        break;

      case 'medium':
        config.width = '880px';
        config.height = 'auto';
        config.maxHeight = '95%';
        break;

      case 'large':
        config.width = '1280px';
        config.maxHeight = '95%';
        break;

      case 'full':
        config.width = '95%';
        config.height = '95%';
        break;

      default:
        break;
    }


  }


  private configureAnimation(config: RadDialogConfig) {


    if(config.placement == 'side'){
      config.animation = { to: 'left' };
      return;
    }

    switch (config.entrance??'bottom'){

      case 'bottom':
        config.animation = { to: 'top' };

        break;
      case 'top':
        config.animation = { to: 'bottom' };
        break;
      case 'left':
        config.animation = { to: 'right' };
        break;
      case 'right':
        config.animation = { to: 'left' };
        break;



    }

  }



  private configureOptions(config: RadDialogConfig) {
    const standardConfig = Object.assign({}, config)
    standardConfig.panelClass = 'rad-dialog-panel';
    return standardConfig;
  }





  showAside<T, R = any>(componentRef: ComponentType<T> | TemplateRef<T>, data?: any,  options?: Partial<RadDialogConfig>): Observable<DialogResult<R>> {

    const metaConfig = DialogMetadata.getConfig(componentRef) || {};
    //const mconfig: DialogConfigOptions = getDialogConfig(componentRef) || {};

    let config: RadDialogConfig = {...this.defaultConfig, ...metaConfig, ...options, ...{entrance: 'right'}};

    this.configureSize(config);
    config = this.configureOptions(config);
    this.configureAnimation(config);


    config.height = '100%';
    config.maxHeight = '100%';
    config.hasBackdrop = true;
    config.position = { right: '0px', top: '50px' };
    config.panelClass = 'rad-dialog-aside-panel';

    if (data) {
      config.data = data;
    }

    const strategy = new GlobalPositionStrategy();
   strategy.right("0px");



    config.positionStrategy = strategy;
    config.disableClose = true;


    if (config.modal) {

      config.hasBackdrop = true;
    }

    const dialogRef = this.showAnimated(componentRef, config);



    return dialogRef.closed.pipe(mergeMap(value => {
      return of(this.getDialogResult<R>(value));
    }));


  }

  private defaultConfig: RadDialogConfig ={
    size: 'medium',
    modal: true,
    entrance: 'bottom'
  }



  private showPlain<T, D = any, R = any>(
    componentOrTemplateRef: ComponentType<T> | TemplateRef<T>,
    config?: RadDialogConfig,
  ): any {


    const ref = this.dialog.open(componentOrTemplateRef, config)

    return ref;

  }

  private showAnimated<T, D = any, R = any>(
    componentOrTemplateRef: ComponentType<T> | TemplateRef<T>,
    config?: RadDialogConfig,
  ): DialogRef {

    const dir: 'ltr' | 'rtl' =
      config.direction || (document.querySelectorAll('[dir="rtl"]').length ? 'rtl' : 'ltr');
    config.direction = config.direction || dir;
    if (config.animation) {
      if (config.animation.to === 'aside') {
        config.animation.to = dir === 'rtl' ? 'left' : 'right';
      }
    }

    if (config.position && config.position.rowEnd) {
      if (dir === 'rtl') {
        config.position.right = config.position.rowEnd;
      } else {
        config.position.left = config.position.rowEnd;
      }
    }

    if (config.position && config.position.rowStart) {
      if (dir === 'rtl') {
        config.position.left = config.position.rowStart;
      } else {
        config.position.right = config.position.rowStart;
      }
    }

    const dialogId = this.getDialogId();
    config.id = dialogId;

    const ref = this.dialog.open(componentOrTemplateRef, config);
    const container = ref.overlayRef.overlayElement.getElementsByTagName('cdk-dialog-container')[0] as HTMLElement;


    if (config.animation) {
      const incomingOptions: DialogAnimationOption = config.animation.incomingOptions ||
        //this.incomingOptions ||
        { keyframeAnimationOptions: { duration: 150, easing: 'ease-in' } };

      const outgoingOptions: DialogAnimationOption = config.animation.outgoingOptions ||
        //this.outgoingOptions ||
        { keyframeAnimationOptions: { duration: 150, easing: 'ease-out' } };

      const wrapper = this.findAncestor(container, 'cdk-global-overlay-wrapper');

      if(!wrapper){
        return ref;
      }

      const animate = (keyframes, options) => {
        return wrapper.animate(keyframes, options);
      };
      //const _afterClosed = new Subject();
      //ref.closed = () => {
      //    return _afterClosed.asObservable();
      //};
      const closeFunction = ref.close;

      let incomeKeyFrames = incomingOptions.keyframes;
      let outgoingKeyFrames = outgoingOptions.keyframes;
      if (config.animation.to) {
        const to = directionMap[config.animation.to];
        const keyFrame100 = {};
        const keyFrame0 = {};
        keyFrame0[to] = 0;
        keyFrame100[to] =
          to === 'top' || to === 'bottom'
            ? container.clientHeight * multyMap[config.animation.to] + 'px'
            : container.clientWidth * multyMap[config.animation.to] + 'px';
        incomeKeyFrames = incomeKeyFrames || [keyFrame100, keyFrame0];
        outgoingKeyFrames = outgoingKeyFrames || [keyFrame0, keyFrame100];
      }
      animate(incomeKeyFrames, incomingOptions.keyframeAnimationOptions);
      const closeHandler = (dialogResult?: R) => {
        //_afterClosed.next(dialogResult);
        const animation = animate(outgoingKeyFrames, outgoingOptions.keyframeAnimationOptions);
        animation.onfinish = () => {
          (wrapper as HTMLElement).style.display = 'none';
          this.ngZone.run(() => ref.close(dialogResult));
        };
        ref.close = closeFunction;
      };
      ref.close = (dialogResult?: R) => closeHandler(dialogResult);
    }

    return ref;
  }

  private getDialogId(): string {
    this.idCounter++;
    const id = `rad-dialog-${this.idCounter}`;
    return id;
  }


  private getDialogContainer(dialogId: string): HTMLElement {

    const containers = document.getElementsByTagName('cdk-dialog-container');
    // if (containers.length == 1) {
    //   return containers.item(0) as HTMLLIElement
    // }
    for (let index = 0; index < containers.length; index++) {
      const element = containers.item(0);
      const id = element.getAttribute("id");
      if (id == dialogId) {
        return containers.item(index) as HTMLElement
      }

    }
    return undefined;

  }



  private findAncestor (el: HTMLElement, cls) {
    while ((el = el.parentElement) && !el.classList.contains(cls));
    return el;
  }
}
