<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden w-full h-full max-h-full">

    <mat-drawer-container class="flex-auto h-full">

        <!-- Drawer -->
        <mat-drawer
            class="dark:bg-gray-900 border-l-gray-300 overflow-hidden h-full max-h-full"
            [autoFocus]="false"
            [mode]="drawerMode"
            [opened]="drawerOpened"
            [position]="'end'"
            [style.width]="widthClass()"
            #matDrawer>
            <!-- Detail view -->
            <ng-container contentRegion></ng-container>
        </mat-drawer>

        <!-- Drawer content -->
        <mat-drawer-content class="bg-slate-50">
                Text
            

        </mat-drawer-content>

    </mat-drawer-container>
   
</div>
