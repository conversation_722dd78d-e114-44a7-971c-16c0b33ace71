import { Injectable, inject } from '@angular/core';
import {
  RegistrationApiProxy,
  SignUpParentInput,
  SignUpParentOutput,
} from '../model/api-proxy';
import { Observable, firstValueFrom } from 'rxjs';

export class RegisterParentAction extends SignUpParentInput {}

@Injectable({ providedIn: 'root' })
export class RegistrationFacade {
  private service = inject(RegistrationApiProxy);

  async registerParent(
    action: RegisterParentAction
  ): Promise<SignUpParentOutput> {
    const input = new SignUpParentInput(action);
    return await firstValueFrom(this.service.signUpParent(input));
  }
}
