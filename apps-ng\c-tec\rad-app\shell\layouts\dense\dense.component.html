<!-- Navigation -->
<rad-sidebar class="dark bg-slate-900 print:hidden" [appearance]="navigationAppearance()"
    [mode]="sideBarMode()" [name]="'mainNavigation'" [navigation]="menuItems()"
    [opened]="!isScreenSmall()">
    <!-- Navigation header hook -->
    <ng-container radSidebarHeader>
        <!-- Logo -->
        <div class="flex flex-row  items-center justify-center h-12 max-h-12 px-2 border-b border-b-gray-800 dark:border-b-gray-200">
            <div class="logo-image">
                <img class="w-7" [src]="logo">
            </div>

            <div class="logo-text flex-auto flex-row items-center justify-between pl-4 pr-2 w-full text-md font-medium text-gray-50">
                <span class="text-gray-900 ">{{applicationName}}</span>
               
                    <button class="btn btn-icon btn-light btn-xs btn-outline" (click)="toggleNavigationAppearance()">
                        <i class="ki-outline ki-left transition-transform" [ngClass]="{'rotate-180': !isExpanded()}">
                        </i>
                    </button>

            </div>
        </div>
    </ng-container>
    <!-- Navigation footer hook -->
    <ng-container radSidebarFooter>
        <!-- User -->
        <div class="flex items-center w-full px-6 py-8 border-t border-t-gray-800 dark:border-t-gray-200">
        </div>
    </ng-container>
</rad-sidebar>

<!-- Wrapper -->
<div class="flex flex-col flex-auto w-full min-w-0 bg-slate-900 ">

    <!-- Header -->
     
    <div class="header-wrapper flex flex-0 items-center w-full bg-white">
        <div
            class="app-header relative flex flex-0 items-center w-full h-10 max-h-12 pl-0 pr-4 z-49 shadow dark:shadow-none dark:border-b bg-card dark:bg-slate-900 print:hidden">
            <!-- <div class="flex items-center justify-center space-x-2 w-15 h-full">
                <img
                    class="w-8"
                    src="assets/images/logo.svg"
                    alt="Logo image">
                <button class="btn btn-xs btn-icon btn-light btn-outline" (click)="toggleNavigation('mainNavigation')">
                    <i class="ri-menu-fold-line"></i>
                </button>
            </div> -->
            <div class="h-full">
                <rad-shell-tab-bar></rad-shell-tab-bar>
            </div>
            <!-- Components -->
            <!-- <div class="flex items-center pl-2 ml-auto space-x-2">
                <rad-fullscreen></rad-fullscreen>
                <rad-shell-user-menu></rad-shell-user-menu>
            </div> -->
        </div>
    </div>
    <div class="flex flex-auto w-full pr-0 pb-0">
        <div class="flex flex-row flex-auto w-full bg-white overflow-hidden">
            <rad-router-outlet></rad-router-outlet>
            <!-- Content -->
            <!-- <div class="flex flex-col flex-auto animation">
        
                <router-outlet #mainOutlet = "outlet" *ngIf="true"></router-outlet>
            </div> -->
        </div>
    </div>
</div>