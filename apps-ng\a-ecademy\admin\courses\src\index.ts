import { ModuleInfo } from '@tec/rad-core/abstractions';
import { courseResolverFn } from './feature-course';
import { radCompositeRoute } from '@tec/rad-core/composition';

export const StudioCoursesModule: ModuleInfo = {
    name: 'CoursesModule',
    path: 'courses',
    routes: [
        {
            name: 'CourseList', path: 'list',
            loadComponent: () => import('./feature-courses').then(m => m.CourseList),
        },
        {
            name: 'Course', path: ':courseId', data: {tabPage:true, title:'Course'},
            loadComponent: () => import('./feature-course').then(m => m.CoursePage),
            resolve: {structure: courseResolverFn},
            children: [
                ...radCompositeRoute('CourseDetail'),
            ]
        },
    ],
    views: [
        {name: 'CourseDetail', loadComponent: () => import('./feature-course-detail').then(m => m.CourseDetailView)}
    ]
};
