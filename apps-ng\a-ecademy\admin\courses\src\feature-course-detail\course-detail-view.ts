import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  inject,
  resource,
  signal,
  input,
  OnInit,
} from '@angular/core';
import { IServiceState } from '@tec/rad-core/abstractions';
import { RadContentLayout, RadDialogFooter, RadPage, RadPanel } from '@tec/rad-ui/layout';
import { CourseModel } from '../core';
import { RadSignalBase } from '@tec/rad-core/services';
import { CourseFacade } from '../core';
import { injectRouteParam } from '@tec/rad-core/utils';
import { CourseDetialController } from './course-detial-controller';
import { RadLookupService } from '@tec/rad-xui/flex';
import { RadFormLayout, RadFormConfig } from '@tec/rad-xui/form';
import { FormGroup } from '@angular/forms';

interface DetailState extends IServiceState {
  course: CourseModel;
}

@Component({
  selector: 'ed-course-detail-view',

  styles: [],
  imports: [CommonModule, RadPage, RadFormLayout,  RadPanel, RadDialogFooter, RadContentLayout],
  providers: [CourseDetialController],
  template: `
    <rad-page layout="card" title="Course Detail" footer="true">
      <rad-content-layout>
        <div class="flex flex-col space-y-6 p-6">
          <rad-panel title="Details">
            <rad-form-layout [form]="controller.form" [items]="fields" [model]="controller.course()" />
          </rad-panel>
        </div>
        <rad-dialog-footer radContentFooter (confirm)="update()" confirmLabel="Update"></rad-dialog-footer>
      </rad-content-layout>
    </rad-page>
  `,
})
export class CourseDetailView implements OnInit {
  protected controller = inject(CourseDetialController);
  protected lookup = inject(RadLookupService);
  #id = injectRouteParam('courseId');

  form = new FormGroup({});

  fields: RadFormConfig = [
    {
      row: [
        { key: 'name', type: 'input', label: 'Name', span:6 },
        { key: 'courseCode', type: 'input', label: 'Code', span:3, required:true },
       
      ],
    },
    {
      row: [
         {
          key: 'gradeLevel', type: 'select', span:3, required:true,
          values: this.lookup.getLookup$('GradeLevel'),
        },
        { key: 'status', type: 'select', span:3, values: this.lookup.getLookup$('CourseStatus')},
        
      ],
    },

    { key: 'description', type: 'textarea' },
  ];

  async ngOnInit(): Promise<void> {
    await this.controller.init(this.#id);
  }

  async update() {
    if(!this.controller.form.validate()){
      return;
    }

    const course = this.controller.course();
    //await this.controller.courseFacade.updateCourse(course);
  }
}
