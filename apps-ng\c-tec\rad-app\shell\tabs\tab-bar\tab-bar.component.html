

<div class="app-tab-bar">
  
    <div class="app-tab active"
         *ngFor="let tab of tabs; let i = index;"
         [class.active]="tab.isActive"
         [class.with-close-button]="!tab.isDefault"
         [class.with-subtitle]="true"
         (click)="tabClicked(tab)"
         (auxclick)="onTabClicked($event, tab)" 
         pTooltip="{{tab.subtitle}}" tooltipPosition="bottom"
         >
         <div class="tab-content">
          <span class="title">{{ tab.title }}</span>
          <span class="subtitle">{{ tab.subtitle }}</span>
          
         </div>

      <button class="btn btn-xs btn-icon tab-close" (click)="closeTab(tab)" *ngIf="i > 0">
        <i class="ri-close-line"></i>
      </button>
    </div>
  </div>


