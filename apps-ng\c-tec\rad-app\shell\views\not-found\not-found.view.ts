import { DialogRef } from '@angular/cdk/dialog';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { RadHeader } from '@tec/rad-ui/layout';
import { Button, ButtonModule } from 'primeng/button';
import { injectViewContext } from "@tec/rad-core/composition";
        
@Component({
    selector: 'rad-not-found',
    templateUrl: './not-found.view.html',
    styleUrls: ['./not-found.view.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        RadHeader,
        ButtonModule
        //Add required imports here
    ]
})
export class NotFoundView {
        

    context = injectViewContext();
    dialogref = inject(DialogRef, {optional:true})
    viewName = this.context?.data?.view;
    
    get inDialog(){
        return this.dialogref;
    }
        
    close(){
        this.dialogref?.close();
    }

        
}