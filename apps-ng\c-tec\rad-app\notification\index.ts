import {EnvironmentProviders,  importProvidersFrom,  Provider} from "@angular/core";
import { AppNotificationService } from "@tec/rad-core/abstractions";

import { RadPrimeNotificationsService } from "./prime/prime-notification.service";
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';




export function provideRadAppNotification(): Array<Provider | EnvironmentProviders> {
  return [

    MessageService,
    { provide: AppNotificationService, useExisting: RadPrimeNotificationsService },


  ]
}
