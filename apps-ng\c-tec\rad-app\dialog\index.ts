
import { DialogModule, DEFAULT_DIALOG_CONFIG, DialogConfig } from "@angular/cdk/dialog";
import {EnvironmentProviders,  Provider, importProvidersFrom} from "@angular/core";
import { AppDialogService } from "@tec/rad-core/abstractions";
import { RadAppDialogService }from "./dialog.service";



const defaultDialogConfig: DialogConfig = {
  hasBackdrop: false,
  panelClass: 'rad-dialog-panel',
}
export function provideRadAppDialog(): Array<Provider | EnvironmentProviders> {
  return [
    importProvidersFrom(DialogModule),
    { provide: AppDialogService, useExisting: RadAppDialogService},
    {provide: DEFAULT_DIALOG_CONFIG, useValue: defaultDialogConfig},
  ];
}
