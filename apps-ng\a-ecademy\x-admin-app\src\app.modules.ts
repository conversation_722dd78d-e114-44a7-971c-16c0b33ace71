import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModuleInfo } from '@tec/rad-core/abstractions';
import { StudioCoursesModule } from '@ed/admin-courses';
import { CourseRunsModule } from '@ed/admin-course-runs';





export const INFO: ModuleInfo = {
  name: 'AdminAp',
  path: '',
  parts:[
    StudioCoursesModule,
    CourseRunsModule

  ]
};

@NgModule({
  imports: [CommonModule],
  providers: [],
})
export class AppModules {
  static forApp(): ModuleInfo {
    return INFO;
  }
}
