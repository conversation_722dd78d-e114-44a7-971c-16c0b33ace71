import { Component, computed, OnD<PERSON>roy, OnInit, signal } from '@angular/core';
import { FieldWrapper, FormlyFieldConfig } from '@ngx-formly/core';
import { RadFormFieldProps } from '../../form-types';
import { isObservable, Subscription } from 'rxjs';
import { CommonModule, DatePipe } from '@angular/common';
import { InfoBlock } from './info-block';
import { compileClasses } from '@tec/rad-ui/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'rad-form-field-info',
  imports: [CommonModule, ReactiveFormsModule, DatePipe, InfoBlock],
  template: `
    <div class="rad-field p-field" [class]="fieldClasses()">
      @if (layout === 'horizontal') {
        <div class="content-wrapper">
          <div class="label-wrapper" [style.width]="props.labelWidth || '120px'">
            <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
              {{ props.label }}
            </label>
          </div>
          <div class="content-wrapper">
            <div class="component-wrapper">
              <rad-info-block [value]="displayValue()" [options]="props.info"></rad-info-block>
            </div>
          </div>
        </div>
      } @else {
        <div class="info-icon" *ngIf="icon">
          <i [ngClass]="icon"></i>
        </div>
        <div class="content-wrapper">
          <div class="label-wrapper">
            <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
              {{ props.label }}
            </label>
          </div>
          <div class="content-wrapper">
            <div class="component-wrapper">
              <rad-info-block [value]="displayValue()" [options]="props.info"></rad-info-block>
            </div>
          </div>
        </div>
      }
    </div>
  `,


})
export class RadFormFieldInfo extends FieldWrapper<FormlyFieldConfig<RadFormFieldProps>> implements OnInit, OnDestroy {
  private optionsSub?: Subscription;
  displayValue = signal('');

  constructor(private datePipe: DatePipe) {
    super();
  }

  get layout() {
    return (this.props.layout || this.options.formState?.layout) ?? 'vertical';
  }

  get icon() {
    return this.props.info?.icon;
  }

  fieldClasses = computed(() => {
    return compileClasses([
      'rad-field-info',
      this.layout === 'horizontal' ? 'rad-field-horizontal' : 'rad-field-vertical',
    ]);
  });

  ngOnInit() {
    this.setupDisplayValue();
    this.formControl.valueChanges.subscribe(() => this.setupDisplayValue());
  }

  ngOnDestroy() {
    this.optionsSub?.unsubscribe();
  }

  setupDisplayValue() {
    const value = this.formControl?.value;

    if (isObservable(this.props.options)) {
      this.optionsSub = this.props.options.subscribe((opts: any[]) => {
        const displayLabel = this.resolveDisplayValue(value, opts);
        this.displayValue.set(displayLabel);
      });
    } else if (Array.isArray(this.props.options)) {
      this.displayValue.set(this.resolveDisplayValue(value, this.props.options));
    } else {
      this.displayValue.set(this.resolveDisplayValue(value));
    }
  }

  resolveDisplayValue(value: any, options?: any[]) {
    if (options && value !== undefined && value !== null && value !== '') {
      const match = options.find(o => o.value === value);
      if (match) return match.label;
    }

    if (this.isDate(value)) {
      const dateFormat = this.props.format || 'yyyy-MM-dd';
      return this.datePipe.transform(value, dateFormat) ?? '—';
    }

    return (value === null || value === undefined || value === '') ? '—' : value;
  }

  isDate(val: any): boolean {
    return this.props.type === 'datepicker' || this.props.type === 'datetime';
  }
}