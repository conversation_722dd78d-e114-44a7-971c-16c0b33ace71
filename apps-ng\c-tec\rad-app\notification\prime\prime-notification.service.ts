import { inject, Injectable } from "@angular/core";
import { AppNotificationService, ColorContext } from "@tec/rad-core/abstractions";
import { RadColor } from "@tec/rad-ui/common";
import { MessageService, ToastMessageOptions } from "primeng/api";


@Injectable({providedIn:'root'})
export class RadPrimeNotificationsService extends AppNotificationService {

    #messageService = inject(MessageService);

  info(message: string, title = 'Info', options?: any): void{
    this.showToast(message, title, 'info');
  }

  success(message: string, title = 'Success', options?: any): void{
    this.showToast(message, title, 'success')
  }

  warn(message: string, title = 'Warning', options?: any): void{
    this.showToast(message, title, 'warning')
  }

  error(message: string, title= 'Error', options?: any): void{
    this.showToast(message, title, 'error')
  }

  showMessage(msg:string){
    this.showToast(msg)
  }

  private showToast(msg = 'Example notification text.', title="", color: ColorContext = 'info' , position = 'top', showCloseButton = true, duration = 3000) {

    const note: ToastMessageOptions = {
        severity: color,
        summary: title,
        detail: msg,
        life: duration,
        sticky: !showCloseButton,

        };
        this.#messageService.add(note);
    } 
   
    
  }


