events { 
  worker_connections  1024;  ## Default: 1024 
} 
 
http {
  index index.html;
error_log /etc/nginx/error_log.log debug;

    ## use mime types 
    include /etc/nginx/mime.types; 
 
server {
  listen 80;
  listen [::]:80;
  server_name _;
  root /wwwroot/;

  # Root → Studio (optional, can keep 301)
  location = / { return 302 /admin/; }

  # ✅ Handle /admin?code=... directly (NO REDIRECT; preserves query string)
  location = /admin {
    try_files /admin/index.html =404;
  }

  # SPA deep links under /admin/
  location ^~ /admin/ {
    try_files $uri $uri/ /admin/index.html;
  }

  # Portal (unchanged)
  location = /portal { return 301 /portal/; }
  location ^~ /portal/ { try_files $uri $uri/ /portal/index.html; }

  # Health
  location = /healthz { return 200 "ok\n"; add_header Content-Type text/plain; }
}
 
    ## enable gzip compression 
    gzip on; 
    gzip_vary on; 
    gzip_min_length 256; 
    gzip_proxied any; 
 
    gzip_types 
      ## text/html is always compressed : https://nginx.org/en/docs/http/ngx_http_gzip_module.html 
      text/plain 
      text/css 
      text/javascript 
      application/javascript 
      application/x-javascript 
      application/xml 
      application/json 
      application/ld+json; 
} 