{"name": "ed-admin-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ec", "sourceRoot": "a-ecademy/x-admin-app/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/a-ecademy/x-admin-app", "index": "a-ecademy/x-admin-app/src/index.html", "browser": "a-ecademy/x-admin-app/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "a-ecademy/x-admin-app/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "./a-ecademy/x-admin-app/public", "output": "/assets"}, {"glob": "**/*", "input": "./c-tec/rad-app/assets", "output": "/assets/app"}, {"glob": "**/*", "input": "./c-tec/rad-ui/assets", "output": "/assets/ui"}], "styles": ["a-ecademy/x-admin-app/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "50kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "ed-admin-app:build:production"}, "development": {"buildTarget": "ed-admin-app:build:development"}}, "defaultConfiguration": "development", "options": {"host": "127.0.0.1", "port": 4170}}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "ed-admin-app:build"}}}}