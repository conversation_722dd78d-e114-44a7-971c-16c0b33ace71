import { Observable, Subject } from "rxjs";

export class TabState<T> {
    id: string;
    data: T;
    tab: ITabItem;
  
    constructor(data: T, tab: ITabItem) {
      this.data = data;
      this.tab = tab;
    }
  
  }

  export interface ITabItem {

    id: string;
    isDefault: boolean;
    rootUrl: string;
    get closed$(): Observable<any>;
    get activated$(): Observable<any>;
    setTitle(title: string, subTitle?: string):void;

  }

  export class PageInfo {
    path: string;
    pathKey: string;
    url: string;
    title: string;
    subTitle: string;
    icon: string;
}
