import { Signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormlyFieldConfig, FormlyFieldProps } from '@ngx-formly/core';
import { Observable } from 'rxjs';

export interface FormFieldProps extends FormlyFieldProps {
  [additionalProperties: string]: any;
}

export interface RadFormFieldProps extends FormlyFieldProps {
  hideRequiredMarker?: boolean;
  hideLabel?: boolean;
  infoMode?: boolean;
  labelWidth?: string;
  layout?: 'horizontal' | 'vertical';
  format?: string;
  info?: RadInfoProps;
}

export type RadInfoType = 'default' | 'badge';

export interface RadBadgeInfo {
  color?: string;
  category?: string;
}

export interface RadInfoProps {
  type?: RadInfoType;
  badge?: RadBadgeInfo;
  icon?: string;

}

export type FormFieldType =
  | 'input'
  | 'checkbox'
  | 'hidden'
  | 'select'
  | 'multiselect'
  | 'treeselect'
  | 'listbox'
  | 'textarea'
  | 'date'
  | 'datetime'
  | 'datepicker'
  | 'password';
export type RadFormConfig = RadFormItem[];

export type AllFieldProps = RadFormFieldProps | SelectFieldProps;
export interface RadFormItem extends FormlyFieldConfig {
  key?: string;
  type?: FormFieldType | string;
  label?: string;
  rowNum?: number;
  colNum?: number;
  span?: number;
  required?: boolean;
  values?: any[] | Observable<any[]>;
  valueProp?: string;
  displayProp?: string;
  hidden?: Signal<boolean>;
  info?: RadInfoProps;
  row?: RadFormItem[];
}

export type LayoutType = 'row' | 'col';
export interface LayoutItem {
  layout: LayoutType;
  [key: string | symbol]: any;
}

export type FormItem = RadFormItem | LayoutItem;
export type FormLayout = FormItem[];

export interface FormConfig {
  name?: string;
  fields: RadFormConfig;
}

export interface SelectFieldProps extends FormFieldProps {
  groupProp?: string | ((option: any) => string);
  labelProp?: string | ((option: any) => any);
  valueProp?: string | ((option: any) => boolean);
  disabledProp?: string | ((option: any) => string);
}

interface TextAreaProps extends FormFieldProps {
  autosize?: boolean;
  autosizeMinRows?: number;
  autosizeMaxRows?: number;
}

export class ExpressFormConfig {
  name: string;
  fields: FormlyFieldConfig[];
}

export type FieldLayout = 'horizontal' | 'vertical';

export type FieldMode = 'edit' | 'info';

export interface IEditForm {
  form: FormGroup;
  validate(): boolean;
}
