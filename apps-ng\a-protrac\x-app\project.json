{"name": "protrac-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ec", "sourceRoot": "a-protrac/x-app/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/a-protrac/x-app", "index": "a-protrac/x-app/src/index.html", "browser": "a-protrac/x-app/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "a-protrac/x-app/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "./a-protrac/x-app/public", "output": "/assets"}, {"glob": "**/*", "input": "./c-tec/rad-app/assets", "output": "/assets/app"}, {"glob": "**/*", "input": "./c-tec/rad-ui/assets", "output": "/assets/ui"}], "styles": ["a-protrac/x-app/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "50kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "protrac-app:build:production"}, "development": {"buildTarget": "protrac-app:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4270}}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "protrac-app:build"}}}}