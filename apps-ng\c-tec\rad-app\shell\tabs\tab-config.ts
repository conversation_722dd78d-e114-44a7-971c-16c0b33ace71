import { ActivatedRouteSnapshot, Params } from '@angular/router';
import { AppRouteData } from '@tec/rad-core/abstractions';
import { UrlHelper } from '@tec/rad-core/utils';





export class TabConfig{
    
    showInTab = false;
    
    /**
     * Path that determines what routes belong to this tab
     */
    path: string;


    /**
     * Param key to use to discriminate routes that belong to this tab
     */
    pathKey: string;

    rootUrl: string;

    currentUrl: string;

    title: string;

    subtitle: string;

    queryParams: Params;
}


export class TabConfigHelper {

    static getConfig(route: ActivatedRouteSnapshot): TabConfig|undefined {
        
        const config = TabConfigHelper.createTabConfig(route);

        

        return config;
    }

    static  createTabConfig(route:ActivatedRouteSnapshot): TabConfig {
        const data = route.data;
        const routeUrl = UrlHelper.getResolvedUrl(route)
        let rootPath = UrlHelper.getResolvedUrl(route);
        const config = new TabConfig();
        config.path = data['tabPath'];
        config.pathKey = data['tabPathKey'];
        config.title = data['title'];
        config.subtitle = data['subtitle'];
        config.showInTab = data['showInTab'];

        if(config.path) {
            rootPath = TabConfigHelper.findRootPath(routeUrl, config.path);
        }

        if(!rootPath.startsWith('/')) {
            rootPath = '/' + rootPath;
        }

        if(!rootPath.endsWith('/')) {
            rootPath = rootPath + '/';
        }

        if(config.pathKey) {
            const paramValue = UrlHelper.getRouteSnapshotParameter(config.pathKey, route)
            if(paramValue){
                rootPath = rootPath + paramValue;
            }
        }

        config.rootUrl = rootPath;
        config.currentUrl = routeUrl;
        config.queryParams = route.queryParams;

        


        return config;
    }

    static  getTabConfig(route:ActivatedRouteSnapshot): TabConfig {
        
        const routeUrl = UrlHelper.getResolvedUrl(route)
        const config = new TabConfig();
        const data = route.data as AppRouteData;
        config.currentUrl = routeUrl;

        if(data.title) {
            config.title = data.title;
        }

        if(data.subtitle) {
            config.subtitle = data.subtitle;
        }

        if(!data.tabPage) {
            return config;
        }

        config.showInTab = true;
        
        let rootPath =routeUrl;

        config.path = data.tabPath;
        config.pathKey = data.tabPathKey;

        if(config.path) {
            rootPath = TabConfigHelper.findRootPath(routeUrl, config.path);
        }

        if(!rootPath.startsWith('/')) {
            rootPath = '/' + rootPath;
        }

        

        if(config.pathKey) {
            const paramValue = UrlHelper.getRouteSnapshotParameter(config.pathKey, route)
            if(paramValue){
                rootPath = rootPath + paramValue;
            }
        }

        config.rootUrl = UrlHelper.normalize(rootPath);
        config.currentUrl = UrlHelper.normalize(routeUrl);

        


        return config;
    }

    static findRootPath(url: string, path: string): string {
        let root = url;
        const parts = url.split(path);
        if(parts.length>0) {
            
            root = parts[0] + path;
        }
 
        return root;
     }
    

}


