import { InjectionToken, Provider, Type } from '@angular/core';
import { Data, ResolveData } from '@angular/router';


import { RAD_VIEWS, AppView, RadViews } from '../views/+index';
import { AppRoute } from '../navigation/route.types';
import { AppAction } from '../actions/action.types';


export interface ModuleInfo {
  //Name of the module
  name: string;
  //Starts a new root scope used to resolve names
  scope?:string;
  //Used to determine path module will be placed at
  path?: string;

  //Uses a component to render path
  component?: Type<any>;

  //Data to apply to route configuration
  data?: Data,

  views?: AppView[];
  routes?: AppRoute[];
  parts?: ModuleInfo[];
  providers?: Provider[];
  rootProviders?: Provider[];
  resolve?: ResolveData,
  actions?: AppAction[],
  //Resolver that ensures module is loaded before main application is loaded
  rootResolve?: ResolveData


}



export const MODULE_INFO = new InjectionToken<ModuleInfo>('MODULE_INFO');

export function provideModules(modules: ModuleInfo): Provider[] {
  return [{ provide: MODULE_INFO, multi: true, useValue: modules }];
}

export function provideViews(types: RadViews): Provider[] {
  return [{ provide: RAD_VIEWS, multi: true, useValue: types }];
}



export const APP_MODULE_INFO = new InjectionToken<ModuleInfo>(
  'APP_MODULE_INFO'
);

export function provideAppModules(apps: ModuleInfo[]): Provider[] {
  const providers: Provider[] = [];

  for (const app of apps) {
    providers.push({ provide: APP_MODULE_INFO, multi: true, useValue: app });
    const rootProviders = getRootProviders(app)

    if (rootProviders.length > 0) {
      for (const rootProvider of rootProviders) {
        providers.push(rootProvider)
      }
    }

    // if(app.rootProviders && app.rootProviders.length > 0) {

    // for(const rootProvider of app.rootProviders) {
    //   providers.push(rootProvider)
    // }
    // }
  }

  return providers;
}


function getRootProviders(app: ModuleInfo): Provider[] {
  const providers: Provider[] = []

  if (app.rootProviders) {
    for (const rootProvider of app.rootProviders) {
      providers.push(rootProvider)
    }
  }

  if (app.parts) {
    for (const child of app.parts) {
      const childProviders = getRootProviders(child)
      if (childProviders) {
        providers.push(...childProviders)
      }
    }

  }



  return providers;
}


export class AppModuleConfig {
  name: string;
  remote: string;
  url: string;
  entry: string;
  info: string
}

