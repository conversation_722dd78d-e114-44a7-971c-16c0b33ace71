import { CommonModule } from '@angular/common';
import {Component, computed, inject, resource, signal, input } from '@angular/core';
import { RadInfoProps } from '../../form-types';
import { ColorMapPipe } from '@tec/rad-ui/common';
import { RadBadge } from '@tec/rad-ui/component';

        
@Component({
    selector: 'rad-info-block',
    imports:[CommonModule, ColorMapPipe, RadBadge],
    styles: [],
    template: `

    @switch(type()){
        @case('badge') {
            @if(badge()?.category){
                <rad-badge [color]="value()|colorCategory:badge()?.category">{{ value() }}</rad-badge>
            }@else{
                <rad-badge [color]="badge()?.color">{{ value() }}</rad-badge>
            }
            
        }
        @default {
          <div class="formly-info-value">{{ value() }}</div>
        }
    }
    `,
    
})
export class InfoBlock {
        

    value = input();
    options = input<RadInfoProps>()

    protected type = computed(() =>{
       return this.options()?.type ?? 'default'
    });
    protected badge = computed(() => this.options()?.badge);
    
        

        
}