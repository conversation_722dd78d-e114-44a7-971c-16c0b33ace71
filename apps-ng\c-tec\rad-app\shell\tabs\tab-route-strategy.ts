import {ComponentRef, Injectable} from '@angular/core';
import { ActivatedRouteSnapshot, DetachedRouteHandle, RouteReuseStrategy } from '@angular/router';
import { KeyedCollection, UrlHelper } from '@tec/rad-core/utils';


import { TabConfigHelper } from './tab-config';
import {TabItem} from "./tab-item";
import { TabManagerService } from './tab-manager.service';


interface RouteHandle extends DetachedRouteHandle {
  componentRef: ComponentRef<unknown>
}

class RouteData {

  key = "";
  handle: RouteHandle;
}
@Injectable()
export class TabRouteReuseStrategy implements RouteReuseStrategy {

  storedRoutes = new KeyedCollection<RouteData>();



  tabs: TabItem[] = [];

  constructor(private navigation: TabManagerService) {
    navigation.tabClosed$.subscribe(tab=>{
      this.removeTabRoutes(tab);
    })
    navigation.tabUrlChanging$.subscribe(tab=>{
      if(tab.isDefault) {
        this.removeTabRoutes(tab)
      }

    })
  }

  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {

    let path = this.getPath(route);

    if (!route.routeConfig) return null;
    if (route.routeConfig.loadChildren || route.routeConfig.children ){
      //console.log(`Retrieve: cannot retrieve children found for path ${path}`)
      //return null;
    }


    const tab = this.navigation.findTabRootForUrl(path);

    if(tab === undefined) {
      console.log(`Retrieve: no tab for path ${path}`)
      return null;
    }

    path = path.toLowerCase();
    const item = this.storedRoutes.get(path);

    return item.handle;
  }

  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    const path = this.getPath(route);


    const tab = this.navigation.findTabRootForUrl(path);



    // if(tab === undefined) {
    //   return false;
    // }

    const keyIsPresent = this.storedRoutes.containsKey(path.toLowerCase());
    if(keyIsPresent) {
      console.log(`Should attach route for path ${path}`)
    }else {
      console.log(`Should not attach route for path ${path}`)
    }
    return keyIsPresent;
  }

  shouldDetach(route: ActivatedRouteSnapshot): boolean {

    const path = this.getPath(route);

    const tab = this.navigation.findTabRootForUrl(path);

    if(tab) {
      console.log(`Should detach route for path ${path}`)
      return true;

    }

    console.log(`Should not detach route for path ${path}`)
    return false;
  }

  shouldReuseRoute(future: ActivatedRouteSnapshot, current: ActivatedRouteSnapshot): boolean {
    const futurePath = this.getPath(future);
    const currentPath = this.getPath(current);
    const futureTab = this.navigation.getTabReference(future);
    const currentTab = this.navigation.getTabReference(current);

    const futureTabId = futureTab?.id;
    const currentTabId = currentTab?.id;


    //console.log(`Future path ${futurePath} current path  ${currentPath}`)

    const isSameTab = (futureTabId && currentTabId ) && futureTabId == currentTabId;

    if(future.queryParams) {
      const tab = this.navigation.findTabWithUrl(futurePath);
      if(tab) {
        const s =1;
      }
    }

    const isSameComponent = future.component == current.component;
    const reuse =  (futurePath === currentPath);
    return reuse;
  }

  store(route: ActivatedRouteSnapshot, detachedTree: DetachedRouteHandle | null): void {

    const data = new RouteData();
    const config = TabConfigHelper.getConfig(route);

    data.key = this.getPath(route);

    console.log(`Storing route for path ${data.key}`)

    data.handle = (<any>detachedTree);

    if(data.handle === null) {
      return;
    }

    this.storedRoutes.add(data.key.toLowerCase(), data);
  }

  private getPath(route: ActivatedRouteSnapshot): string {
    let url = UrlHelper.getResolvedUrl(route);
    url = UrlHelper.normalize(url);
    return url.toLowerCase();
  }





  private removeTabRoutes(tab: TabItem) {
    const tabsToRemove = [];
    for(const item of this.storedRoutes.keys()){
      if(tab.isRootOf(item)) {
        tabsToRemove.push(item)
      }
    }

    for(const key of tabsToRemove) {
      const route = this.storedRoutes.get(key);
      if(route.handle.componentRef){
        route.handle.componentRef.destroy();
      }
      this.storedRoutes.remove(key);
    }

  }

}


