import { Injectable, InjectionToken } from '@angular/core';
import { Layout } from '../model/layout.types';
export const RAD_SHELL_CONFIG = new InjectionToken<any>('RAD_SHELL_CONFIG');
// Types
export type Scheme = 'auto' | 'dark' | 'light';
export type Screens = { [key: string]: string };
export type Theme = 'theme-default' | string;
export type Themes = { id: string; name: string }[];

/**
 * AppConfig interface. Update this interface to strictly type your config
 * object.
 */
export interface ExpressAppConfig
{
    layout?: string;
    scheme?: Scheme;
    screens?: Screens;
    theme?: Theme;
    themes?: Themes;
    logo?: string;
}

/**
 * Default configuration for the entire application. This object is used by
 * RadConfigService to set the default configuration.
 *
 * If you need to store global configuration for your app, you can use this
 * object to set the defaults. To access, update and reset the config, use
 * RadConfigService and its methods.
 */
export const defaultShellConfig: ExpressAppConfig = {
    layout: 'dense',
    scheme: 'light',
    screens: {
        sm: '600px',
        md: '960px',
        lg: '1280px',
        xl: '1440px'
    },
    theme  : 'theme-brand',
    themes : [
        {
            id  : 'theme-default',
            name: 'Default'
        },
        {
            id  : 'theme-brand',
            name: 'Brand'
        },
        {
            id  : 'theme-teal',
            name: 'Teal'
        },
        {
            id  : 'theme-rose',
            name: 'Rose'
        },
        {
            id  : 'theme-purple',
            name: 'Purple'
        },
        {
            id  : 'theme-amber',
            name: 'Amber'
        }
    ]
};

@Injectable()
export abstract class RadShellConfigService {

   abstract get logo(): string;

}