:host {
  position: relative;
  height: 100%;
  display: flex;
  align-items: flex-end;

  ::ng-deep .mat-mdc-button-touch-target {
    position: absolute;
    top: 50%;
    height: 18px;
    left: 50%;
    width: 18px;
    transform: translate(-50%, -50%);
  }

  .app-tab-bar {
    display: flex;
    flex-direction: row;
    justify-items: start;
    align-content: center;
    overflow: hidden;
    height: 100%;
    min-height: 100%;

    .app-tab {
      color: var(--gray-500);
      display: flex;
      flex-direction: row;
      justify-content: start;
      min-width: 80px;
      max-width: 160px;
      height: 100%;
      max-height: 100%;
      min-height: 100%;
      background: transparent;

      padding: 4px 8px 2px 8px;
      position: relative;
      border-radius: 0;

      margin-right: 5px;
      transition: all ease-in-out 0.2s;

      .tab-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;

        .title {
          font-size: 13px;
          font-weight: medium;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1;
          margin-bottom: 4px;
        }

        .subtitle {
          font-size: 12px;
          font-weight: normal;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1;
        }
      }

      .close-button2 {
        position: absolute;
        top: 50%;
        height: 16px;
        left: 50%;
        width: 16px;
        transform: translate(-50%, -50%);
      }

      .tab-close {
        display: none;
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translate(-4px, -12px);
        width: 12px;
      }

      &.with-close-button {
        .tab-close {
          display: block;
          visibility: hidden;
        }
      }

      &.with-subtitle {
        .title {
          font-size: 14px;
        }
      }

      &.active {
        border-top: 2px solid var(--rd-primary);
        background-color: var(--slate-50);

        .tab-content {
          .title {
            color: var(--slate-800);
          }

          .subtitle {
            color: var(--slate-500);
          }
        }
      }

      &:hover {
        background-color: var(--slate-50);
        cursor: pointer;

        &.with-close-button {
          .tab-close {
            visibility: visible;
          }
          padding-right: 20px;
        }
      }

      &:not(.active) {
        cursor: pointer;

        &:hover {
          .title {
            color: var(--primary-500);
          }

          .subtitle {
            color: var(--primary-500);
          }

          [mat-icon-button] {
            visibility: visible;
          }
        }
      }
    }
  }
}