import { Injectable, Injector, inject } from "@angular/core";
import { FormlyFieldConfig } from "@ngx-formly/core";
import { RadFormItem, FormConfig,  RadFormConfig } from "./form-types";
import { StringHelper } from "@tec/rad-core/utils";
import { toObservable } from "@angular/core/rxjs-interop";
import { FormlyFieldProps } from "@ngx-formly/material/form-field";
import { runInInjectionContext } from '@angular/core';


export interface customFieldProps extends FormlyFieldProps  {
    rowNum?: number;
    colNum?: number;
    [additionalProperties: string]: any;
}

@Injectable({ providedIn: 'root' })
export class FormBuilder {



    injector = inject(Injector)

    private defaultFields: { [key: string]: FormlyFieldConfig } = {
        'input': {},

    }



    async configureForm(config: FormConfig): Promise<FormlyFieldConfig[]> {
        return this.createFields(config.fields);
    }

    createFields(formConfig: RadFormConfig): FormlyFieldConfig[] {
        const fields: FormlyFieldConfig[] = [];
        let i = 1000;
        if(!formConfig || !formConfig.length || formConfig.length === 0){
            return fields;
        }

        let hasRowLayout = false;

        for (const item of formConfig) {
            const formlyField = runInInjectionContext(this.injector,()=>  this.createField(item.key, item, i));
            fields.push(formlyField);
            if(item.row){
                hasRowLayout = true;
            }
            i++;
        }

       
        if(hasRowLayout){
            return fields;
        }

        

        let rowNumbers = fields.map((field) => {return field.props?.rowNum});
        //get unique rows
        rowNumbers = rowNumbers.filter((value, index, self) => self.indexOf(value) === index);
        rowNumbers = rowNumbers.sort((a,b)=>a-b);

        const formFields: FormlyFieldConfig[] = [];

        //var create row groups
        for (const rowNumber of rowNumbers) {
            const colFields = fields.filter((field) => field.props?.rowNum === rowNumber);
            const rowGroup = this.createRowGroup(colFields);
            formFields.push(rowGroup);

        }

        return formFields;
    }



    createField(key: string, def: RadFormItem, index: number): FormlyFieldConfig {


        if(def.row){
            const items = def.row.map((item) => this.createField(item.key, item, index));
            return this.createRowGroup(items);
        }

        const rowNum = def.rowNum || index;
        const colNum = def.colNum || index;

        const colSpan = def.span || 1;
        const type = def.type ?? 'input';
        
        let selectOptions: any = def.values;
        if (def.values && typeof def.values === 'function') {
            selectOptions = toObservable(def.values);
        }



        const classes = `formly-${key} flex-${colSpan} formly-${type} rad-mat-dense`;
        const className = def.className ? `${def.className} ${classes}` : classes;
        let props: customFieldProps = {
            rowNum: rowNum,
            colNum: colNum,
            type: type.toLowerCase(),
            label: this.getLabel(key, def),
            required: def.required,
            options: selectOptions,
            info: def.info
        }

        def.className = className;

        const typeProps = this.getCustomProps(def);
        if(typeProps){
            props = Object.assign(props, typeProps);
        }

        if(def.props){
            props = Object.assign(props, def.props);
        }

        def.props = props;



        def.expressions = {
            ...this.getDefaultExpressions(),
            ...def.expressions
        }



        return def;
    }


    private createRowGroup(items: FormlyFieldConfig[]): any {
        return {
    
            fieldGroupClassName: 'display-form-flex',
            fieldGroup: items
        }
    }


    private getLabel(key: string, def: RadFormItem): string {
        return def.label || StringHelper.toSentenceCase(key);
    }

    private getCustomProps(def: RadFormItem): customFieldProps {
        if(def.type === 'select' || def.type === 'treeselect') {
            return {
                disableOptionCentering: true,
                labelProp:def.displayProp || 'label',
                valueProp: def.valueProp || 'value',
                appendTo: 'body'
            };
        }
        return undefined;
    }

    private getDefaultExpressions(): any{
        return {
            'props.disabled': 'formState.disabled',
        }
    }

}
