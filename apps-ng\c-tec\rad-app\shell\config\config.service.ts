import { Inject, Injectable } from '@angular/core';
import { RAD_SHELL_CONFIG } from './config.types';
import { merge } from 'lodash-es';
import { BehaviorSubject, Observable } from 'rxjs';




@Injectable({providedIn: 'root'})
export class ShellConfigService
{
    private _config: BehaviorSubject<any>;

    /**
     * Constructor
     */
    constructor(@Inject(RAD_SHELL_CONFIG) config: any)
    {
        // Private
        this._config = new BehaviorSubject(config);
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Setter & getter for config
     */
    set config(value: any)
    {
        // Merge the new config over to the current config
        const config = merge({}, this._config.getValue(), value);

        // Execute the observable
        this._config.next(config);
    }

    
    get config$(): Observable<any>
    {
        return this._config.asObservable();
    }

    get logo(): string {
        return this._config.getValue().logo;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Resets the config to the default
     */
    reset(): void
    {
        // Set the config
        this._config.next(this.config);
    }
}
