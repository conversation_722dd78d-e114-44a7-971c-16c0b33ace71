import { DialogConfig } from '@angular/cdk/dialog';
import { DialogEntrance, DialogPlacement, DialogSize } from '@tec/rad-core/abstractions';


/** Possible overrides for a dialog's position. */
export declare interface DialogPosition {
    /** Override for the dialog's top position. */
    top?: string;
    /** Override for the dialog's bottom position. */
    bottom?: string;
    /** Override for the dialog's left position. */
    left?: string;
    /** Override for the dialog's right position. */
    right?: string;
}





export class EditorInput<T = string|number> {

    constructor(public readonly id: T | undefined = undefined ) {

    }

    get isNew() {
        return this.id != undefined;
    }

}

export interface DialogAnimationOption {
    keyframes?: Keyframe[];
    keyframeAnimationOptions: KeyframeAnimationOptions;
}

export interface AnimatedDialogConfig extends DialogConfig {
    title?: string;
    animation?:
        | {
              to: 'aside' | 'top' | 'bottom' | 'left' | 'right';
              incomingOptions?: { keyframes?: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
              outgoingOptions?: { keyframes?: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
          }
        | {
              to?: 'aside' | 'top' | 'bottom' | 'left' | 'right';
              incomingOptions?: { keyframes: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
              outgoingOptions?: { keyframes: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
          };
    position?: DialogPosition & { rowStart?: string; rowEnd?: string };
}


export interface RadDialogConfig extends DialogConfig {
    modal?: boolean,
    title?: string;
    size?: DialogSize;
    entrance?: DialogEntrance;
    placement?: DialogPlacement;
    animation?:
        | {
              to: 'aside' | 'top' | 'bottom' | 'left' | 'right';
              incomingOptions?: { keyframes?: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
              outgoingOptions?: { keyframes?: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
          }
        | {
              to?: 'aside' | 'top' | 'bottom' | 'left' | 'right';
              incomingOptions?: { keyframes: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
              outgoingOptions?: { keyframes: Keyframe[]; keyframeAnimationOptions: KeyframeAnimationOptions };
          };
    position?: DialogPosition & { rowStart?: string; rowEnd?: string };
}
