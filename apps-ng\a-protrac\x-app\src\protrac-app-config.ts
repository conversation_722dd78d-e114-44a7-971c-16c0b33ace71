import { ApplicationConfig } from '@angular/core';
import { APP_BASE_HREF } from '@angular/common';
import { provideAppModules } from '@tec/rad-core/abstractions';
import { APP_MENU_CONFIG } from './protrac-menu';
import { AppModules } from './protrac-app-modules';
import { provideRadApp } from '@tec/rad-app';
import { provideAppMenu } from '@tec/rad-app/core';
import { provideRadInfraData } from '@tec/rad-infra/data';
export const studioAppConfig: ApplicationConfig = {
  providers: [

    provideRadApp(),
    provideRadInfraData(),
    provideAppModules([AppModules.forApp()]),
    provideAppMenu(APP_MENU_CONFIG),
    { provide: APP_BASE_HREF, useValue: '/admin'}
  
  ]
};