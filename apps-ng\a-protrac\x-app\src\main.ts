import { bootstrapApplication } from '@angular/platform-browser';
import { studioAppConfig } from './protrac-app-config';
import { registerLicense } from '@syncfusion/ej2-base';
import { dxLicenseKey, ej2LicenseKey } from './license';
import { RadAppComponent } from '@tec/rad-app/shell';
import { ApplicationConfig, enableProdMode } from '@angular/core';
import { APP_CONFIG } from '@tec/rad-core/abstractions';
config({licenseKey: dxLicenseKey});
registerLicense(ej2LicenseKey);

// fetch('assets/appconfig.json')
//       .then((res) => res.json())
//       .then((config) => {
//         if (config.environment?.production) {
//           enableProdMode();
//         }

//         const newConfig: ApplicationConfig = {
//           providers:[
//             { provide: APP_CONFIG, useValue: config, multi: true },
//             ...studioAppConfig.providers,
//           ]
         

//         }


//         // platformBrowserDynamic([
//         //   { provide: APP_CONFIG, useValue: config, multi: true },
//         // ])
//         //   .bootstrapModule(moduleType, compilerOptions)
//         bootstrapApplication(RadAppComponent, newConfig)
//           .catch((err) => console.error(err));
//       });

// bootstrapApplication(RadAppComponent, studioAppConfig)
//   .catch((err) => console.error(err));




 import('./bootstrap')
 	.catch(err => console.error(err));
