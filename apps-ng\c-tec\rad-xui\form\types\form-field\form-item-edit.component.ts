import { Component, computed } from '@angular/core';
import { FieldWrapper, FormlyFieldConfig, FormlyModule } from '@ngx-formly/core';
import { RadFormFieldProps } from '../../form-types';
import { compileClasses } from '@tec/rad-ui/common';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'rad-form-field-edit',
  imports: [CommonModule, ReactiveFormsModule, FormlyModule],
  template: `
    <div class="rad-field p-field" [class]="fieldClasses()">
      @if (layout === 'horizontal') {
        <div class="label-wrapper" [style.width]="props.labelWidth || '120px'">
          <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
            {{ props.label }}
            <span class='field-required' *ngIf="props.required && props.hideRequiredMarker !== true" aria-hidden="true">*</span>
          </label>
        </div>
        <div class="content-wrapper">
          <div class="component-wrapper">
            <ng-content></ng-content>
          </div>
          <div class="error-wrapper" style="width:100%;">
            <small *ngIf="showError" class="field-error">
              <formly-validation-message class="ui-message-text" [field]="field"></formly-validation-message>
            </small>
          </div>
        </div>
      } @else {
        <div class="label-wrapper">
          <label *ngIf="props.label && props.hideLabel !== true" [for]="id">
            {{ props.label }}
            <span class='field-required' *ngIf="props.required && props.hideRequiredMarker !== true" aria-hidden="true">*</span>
          </label>
        </div>
        <div class="content-wrapper">
          <div class="component-wrapper">
            <ng-content></ng-content>
          </div>
          <div class="error-wrapper">
            <small *ngIf="showError" class="field-error">
              <formly-validation-message class="ui-message-text" [field]="field"></formly-validation-message>
            </small>
          </div>
        </div>
      }
    </div>
  `
})
export class RadFormFieldEdit extends FieldWrapper<FormlyFieldConfig<RadFormFieldProps>> {
  get layout() {
    return (this.props.layout || this.options.formState?.layout) ?? 'vertical';
  }

  fieldClasses = computed(() => {
    return compileClasses([
      'rad-field-edit',
      this.layout === 'horizontal' ? 'rad-field-horizontal' : 'rad-field-vertical',
    ]);
  });
}