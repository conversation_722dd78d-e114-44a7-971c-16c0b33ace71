// result.operators.ts
import { Observable, of, throwError, from, OperatorFunction } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { ErrorType, Result, ResultError } from './result';

/**
 * RxJS operators for Result pattern
 */
export class ResultOperators {
  /**
   * Converts an Observable to Observable<Result<T>>
   */
  static toResult<T>(errorType: ErrorType = ErrorType.Unexpected): OperatorFunction<T, Result<T>> {
    return (source: Observable<T>): Observable<Result<T>> => {
      return source.pipe(
        map((value: T) => Result.ok<T>(value)),
        catchError((error: any) => of(Result.fromError<T>(error, errorType)))
      );
    };
  }

  /**
   * Unwraps a Result<T> to T, throwing on failure
   */
  static unwrap<T>(): OperatorFunction<Result<T>, T> {
    return (source: Observable<Result<T>>): Observable<T> => {
      return source.pipe(
        switchMap((result: Result<T>) => 
          result.isSuccess 
            ? of(result.value)
            : throwError(() => result.errors)
        )
      );
    };
  }

  /**
   * Maps the inner value of a Result
   */
  static mapResult<T, U>(fn: (value: T) => U): OperatorFunction<Result<T>, Result<U>> {
    return (source: Observable<Result<T>>): Observable<Result<U>> => {
      return source.pipe(
        map((result: Result<T>) => result.map(fn))
      );
    };
  }

  /**
   * FlatMaps the inner value of a Result
   */
  static flatMapResult<T, U>(fn: (value: T) => Result<U>): OperatorFunction<Result<T>, Result<U>> {
    return (source: Observable<Result<T>>): Observable<Result<U>> => {
      return source.pipe(
        map((result: Result<T>) => result.flatMap(fn))
      );
    };
  }

  /**
   * Async FlatMap for operations returning Observables
   */
  static flatMapResultAsync<T, U>(fn: (value: T) => Observable<Result<U>>): OperatorFunction<Result<T>, Result<U>> {
    return (source: Observable<Result<T>>): Observable<Result<U>> => {
      return source.pipe(
        switchMap((result: Result<T>) => 
          result.isSuccess 
            ? fn(result.value)
            : of(Result.fail<U>(result.errors))
        )
      );
    };
  }

  /**
   * Filters successful results and unwraps them
   */
  static filterSuccess<T>(): OperatorFunction<Result<T>, T> {
    return (source: Observable<Result<T>>): Observable<T> => {
      return source.pipe(
        switchMap((result: Result<T>) => 
          result.isSuccess ? of(result.value) : throwError(() => result.errors)
        )
      );
    };
  }

  /**
   * Tap into successful results
   */
  static tapSuccess<T>(fn: (value: T) => void): OperatorFunction<Result<T>, Result<T>> {
    return (source: Observable<Result<T>>): Observable<Result<T>> => {
      return source.pipe(
        map((result: Result<T>) => {
          if (result.isSuccess) {
            fn(result.value);
          }
          return result;
        })
      );
    };
  }

  /**
   * Tap into failed results
   */
  static tapFailure<T>(fn: (errors: ResultError[]) => void): OperatorFunction<Result<T>, Result<T>> {
    return (source: Observable<Result<T>>): Observable<Result<T>> => {
      return source.pipe(
        map((result: Result<T>) => {
          if (result.isFailure) {
            fn(result.errors);
          }
          return result;
        })
      );
    };
  }
}