rad-core-shell {


    display: flex;
    flex: 1 1 auto;
    width: 100%;
    max-width: 100%;
    min-width: 0;
    background-color: var(--rd-body-bg);


    /* Base styles for individual layouts */
    > * {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        width: 100%;
    }

    /* Base styles for components that load as a route */
    router-outlet {

        + * {
            position: relative;
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;
            width: 100%;
            height: 100%;
        }
    }
}
