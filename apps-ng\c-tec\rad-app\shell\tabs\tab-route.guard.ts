import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';


import { TabConfigHelper } from './tab-config';
import { TabManagerService } from './tab-manager.service';




    @Injectable({
        providedIn: 'root'
    })
export class TabRouteGuard  {

    constructor(
        private tabService: TabManagerService,
        private _router: Router,

    ) { }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {

        const config = TabConfigHelper.getTabConfig(route);

        console.log(`In guard for tab ${config.currentUrl}`)
        if(config.showInTab) {
            const info = this.tabService.registerTab(config);
            return true;
        } else {
            if(config.title){
                this.tabService.setTabTitle(route, config.title, config.subtitle);
            }
            return true;
        }
       
    }

    canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
        return this.canActivate(route, state);
    }


   

    
}
