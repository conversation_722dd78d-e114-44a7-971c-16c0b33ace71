//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.5.0.0 (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

export const RAD_SYS_API_URL = new InjectionToken<string>('RAD_SYS_API_URL');

export interface IDiagnosticsApiProxy {
    /**
     * @return OK
     */
    getConfig(): Observable<DiagnosticsResponse>;
}

@Injectable({
    providedIn: 'root'
})
export class DiagnosticsApiProxy implements IDiagnosticsApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return OK
     */
    getConfig(): Observable<DiagnosticsResponse> {
        let url_ = this.baseUrl + "/api/rad-sys/diagnostics/get-config";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetConfig(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetConfig(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<DiagnosticsResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<DiagnosticsResponse>;
        }));
    }

    protected processGetConfig(response: HttpResponseBase): Observable<DiagnosticsResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = DiagnosticsResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IExpressDataApiProxy {
    /**
     * @param entityType (optional) 
     * @return OK
     */
    getEntityInfos(entityType?: string | undefined): Observable<ExpressEntityInfo[]>;
    /**
     * @return OK
     */
    getOptions(): Observable<ValueOptionsGroup[]>;
}

@Injectable({
    providedIn: 'root'
})
export class ExpressDataApiProxy implements IExpressDataApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param entityType (optional) 
     * @return OK
     */
    getEntityInfos(entityType?: string | undefined): Observable<ExpressEntityInfo[]> {
        let url_ = this.baseUrl + "/api/rad-sys/express-data/get-entity-infos?";
        if (entityType === null)
            throw new globalThis.Error("The parameter 'entityType' cannot be null.");
        else if (entityType !== undefined)
            url_ += "entityType=" + encodeURIComponent("" + entityType) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetEntityInfos(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetEntityInfos(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ExpressEntityInfo[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ExpressEntityInfo[]>;
        }));
    }

    protected processGetEntityInfos(response: HttpResponseBase): Observable<ExpressEntityInfo[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(ExpressEntityInfo.fromJS(item));
            }
            else {
                result200 = null as any;
            }
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return OK
     */
    getOptions(): Observable<ValueOptionsGroup[]> {
        let url_ = this.baseUrl + "/api/rad-sys/express-data/get-options";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetOptions(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetOptions(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ValueOptionsGroup[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ValueOptionsGroup[]>;
        }));
    }

    protected processGetOptions(response: HttpResponseBase): Observable<ValueOptionsGroup[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(ValueOptionsGroup.fromJS(item));
            }
            else {
                result200 = null as any;
            }
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IFormsApiProxy {
    /**
     * @return OK
     */
    getFormDefinitions(): Observable<FormDefinition[]>;
}

@Injectable({
    providedIn: 'root'
})
export class FormsApiProxy implements IFormsApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return OK
     */
    getFormDefinitions(): Observable<FormDefinition[]> {
        let url_ = this.baseUrl + "/api/rad-sys/forms/get-form-definitions";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFormDefinitions(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFormDefinitions(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FormDefinition[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FormDefinition[]>;
        }));
    }

    protected processGetFormDefinitions(response: HttpResponseBase): Observable<FormDefinition[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(FormDefinition.fromJS(item));
            }
            else {
                result200 = null as any;
            }
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface ILookupApiProxy {
    /**
     * @param group (optional) 
     * @return OK
     */
    getLookups(group?: string | undefined): Observable<GetLookupsResult>;
}

@Injectable({
    providedIn: 'root'
})
export class LookupApiProxy implements ILookupApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param group (optional) 
     * @return OK
     */
    getLookups(group?: string | undefined): Observable<GetLookupsResult> {
        let url_ = this.baseUrl + "/api/rad-sys/lookup/get-lookups?";
        if (group === null)
            throw new globalThis.Error("The parameter 'group' cannot be null.");
        else if (group !== undefined)
            url_ += "Group=" + encodeURIComponent("" + group) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetLookups(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetLookups(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetLookupsResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetLookupsResult>;
        }));
    }

    protected processGetLookups(response: HttpResponseBase): Observable<GetLookupsResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetLookupsResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IRadSysApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    createUpdateUser(body?: ICreateUpdateUserRequest | undefined): Observable<CreateUpdateUserResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    updatePassword(body?: IUpdatePasswordRequest | undefined): Observable<UpdatePasswordResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    createUpdateRole(body?: ICreateUpdateRoleRequest | undefined): Observable<CreateUpdateRoleResult>;
    /**
     * @param body (optional) 
     * @return OK
     */
    getRoles(body?: any | undefined): Observable<GetRolesResult>;
}

@Injectable({
    providedIn: 'root'
})
export class RadSysApiProxy implements IRadSysApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createUpdateUser(body?: ICreateUpdateUserRequest | undefined): Observable<CreateUpdateUserResult> {
        let url_ = this.baseUrl + "/api/rad-sys/create-update-user";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateUpdateUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateUpdateUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CreateUpdateUserResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CreateUpdateUserResult>;
        }));
    }

    protected processCreateUpdateUser(response: HttpResponseBase): Observable<CreateUpdateUserResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CreateUpdateUserResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updatePassword(body?: IUpdatePasswordRequest | undefined): Observable<UpdatePasswordResult> {
        let url_ = this.baseUrl + "/api/rad-sys/update-password";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdatePassword(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdatePassword(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<UpdatePasswordResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<UpdatePasswordResult>;
        }));
    }

    protected processUpdatePassword(response: HttpResponseBase): Observable<UpdatePasswordResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = UpdatePasswordResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createUpdateRole(body?: ICreateUpdateRoleRequest | undefined): Observable<CreateUpdateRoleResult> {
        let url_ = this.baseUrl + "/api/rad-sys/create-update-role";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateUpdateRole(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateUpdateRole(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<CreateUpdateRoleResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<CreateUpdateRoleResult>;
        }));
    }

    protected processCreateUpdateRole(response: HttpResponseBase): Observable<CreateUpdateRoleResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = CreateUpdateRoleResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    getRoles(body?: any | undefined): Observable<GetRolesResult> {
        let url_ = this.baseUrl + "/api/rad-sys/get-roles";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetRoles(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetRoles(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetRolesResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetRolesResult>;
        }));
    }

    protected processGetRoles(response: HttpResponseBase): Observable<GetRolesResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetRolesResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IRoleListApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfAppRoleInfo | undefined): Observable<ListOutputOfAppRoleInfo>;
    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult>;
}

@Injectable({
    providedIn: 'root'
})
export class RoleListApiProxy implements IRoleListApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfAppRoleInfo | undefined): Observable<ListOutputOfAppRoleInfo> {
        let url_ = this.baseUrl + "/api/rad-sys/role-list/query-list";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processQueryList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processQueryList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ListOutputOfAppRoleInfo>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ListOutputOfAppRoleInfo>;
        }));
    }

    protected processQueryList(response: HttpResponseBase): Observable<ListOutputOfAppRoleInfo> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ListOutputOfAppRoleInfo.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult> {
        let url_ = this.baseUrl + "/api/rad-sys/role-list/get-filter";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFilter(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFilter(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FilterResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FilterResult>;
        }));
    }

    protected processGetFilter(response: HttpResponseBase): Observable<FilterResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FilterResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IStandardListApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfStandardItem | undefined): Observable<ListOutputOfStandardItem>;
    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult>;
}

@Injectable({
    providedIn: 'root'
})
export class StandardListApiProxy implements IStandardListApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfStandardItem | undefined): Observable<ListOutputOfStandardItem> {
        let url_ = this.baseUrl + "/api/rad-sys/standard-list/query-list";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processQueryList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processQueryList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ListOutputOfStandardItem>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ListOutputOfStandardItem>;
        }));
    }

    protected processQueryList(response: HttpResponseBase): Observable<ListOutputOfStandardItem> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ListOutputOfStandardItem.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult> {
        let url_ = this.baseUrl + "/api/rad-sys/standard-list/get-filter";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFilter(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFilter(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FilterResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FilterResult>;
        }));
    }

    protected processGetFilter(response: HttpResponseBase): Observable<FilterResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FilterResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface IUserListApiProxy {
    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfAppUserInfo | undefined): Observable<ListOutputOfAppUserInfo>;
    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult>;
}

@Injectable({
    providedIn: 'root'
})
export class UserListApiProxy implements IUserListApiProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(RAD_SYS_API_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    queryList(body?: IListRequestOfAppUserInfo | undefined): Observable<ListOutputOfAppUserInfo> {
        let url_ = this.baseUrl + "/api/rad-sys/user-list/query-list";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processQueryList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processQueryList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ListOutputOfAppUserInfo>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ListOutputOfAppUserInfo>;
        }));
    }

    protected processQueryList(response: HttpResponseBase): Observable<ListOutputOfAppUserInfo> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ListOutputOfAppUserInfo.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return OK
     */
    getFilter(): Observable<FilterResult> {
        let url_ = this.baseUrl + "/api/rad-sys/user-list/get-filter";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFilter(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFilter(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FilterResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FilterResult>;
        }));
    }

    protected processGetFilter(response: HttpResponseBase): Observable<FilterResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FilterResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = RemoteServiceErrorResponse.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = RemoteServiceErrorResponse.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = RemoteServiceErrorResponse.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = RemoteServiceErrorResponse.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result500: any = null;
            let resultData500 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result500 = RemoteServiceErrorResponse.fromJS(resultData500);
            return throwException("Internal Server Error", status, _responseText, _headers, result500);
            }));
        } else if (status === 501) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result501: any = null;
            let resultData501 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result501 = RemoteServiceErrorResponse.fromJS(resultData501);
            return throwException("Not Implemented", status, _responseText, _headers, result501);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class AppConfigOptions implements IAppConfigOptions {
    name: string | undefined;
    selfUrl: string | undefined;
    clientUrl: string | undefined;
    corsOrigins: string | undefined;
    forwardHeaders: boolean;
    enableSwagger: boolean;

    constructor(data?: Partial<IAppConfigOptions>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"];
            this.selfUrl = _data["selfUrl"];
            this.clientUrl = _data["clientUrl"];
            this.corsOrigins = _data["corsOrigins"];
            this.forwardHeaders = _data["forwardHeaders"];
            this.enableSwagger = _data["enableSwagger"];
        }
    }

    static fromJS(data: any): AppConfigOptions {
        data = typeof data === 'object' ? data : {};
        let result = new AppConfigOptions();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name;
        data["selfUrl"] = this.selfUrl;
        data["clientUrl"] = this.clientUrl;
        data["corsOrigins"] = this.corsOrigins;
        data["forwardHeaders"] = this.forwardHeaders;
        data["enableSwagger"] = this.enableSwagger;
        return data;
    }

    clone(): AppConfigOptions {
        const json = this.toJSON();
        let result = new AppConfigOptions();
        result.init(json);
        return result;
    }
}

export interface IAppConfigOptions {
    name: string | undefined;
    selfUrl: string | undefined;
    clientUrl: string | undefined;
    corsOrigins: string | undefined;
    forwardHeaders: boolean;
    enableSwagger: boolean;
}

export class AppRoleInfo implements IAppRoleInfo {
    id: string;
    roleName: string | undefined;
    users: string | undefined;

    constructor(data?: Partial<IAppRoleInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.roleName = _data["roleName"];
            this.users = _data["users"];
        }
    }

    static fromJS(data: any): AppRoleInfo {
        data = typeof data === 'object' ? data : {};
        let result = new AppRoleInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["roleName"] = this.roleName;
        data["users"] = this.users;
        return data;
    }

    clone(): AppRoleInfo {
        const json = this.toJSON();
        let result = new AppRoleInfo();
        result.init(json);
        return result;
    }
}

export interface IAppRoleInfo {
    id: string;
    roleName: string | undefined;
    users: string | undefined;
}

export class AppUserInfo implements IAppUserInfo {
    id: string;
    fullName: string | undefined;
    userName: string | undefined;
    email: string | undefined;
    roles: string | undefined;
    status: string | undefined;

    constructor(data?: Partial<IAppUserInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fullName = _data["fullName"];
            this.userName = _data["userName"];
            this.email = _data["email"];
            this.roles = _data["roles"];
            this.status = _data["status"];
        }
    }

    static fromJS(data: any): AppUserInfo {
        data = typeof data === 'object' ? data : {};
        let result = new AppUserInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fullName"] = this.fullName;
        data["userName"] = this.userName;
        data["email"] = this.email;
        data["roles"] = this.roles;
        data["status"] = this.status;
        return data;
    }

    clone(): AppUserInfo {
        const json = this.toJSON();
        let result = new AppUserInfo();
        result.init(json);
        return result;
    }
}

export interface IAppUserInfo {
    id: string;
    fullName: string | undefined;
    userName: string | undefined;
    email: string | undefined;
    roles: string | undefined;
    status: string | undefined;
}

export class CreateUpdateRoleRequest implements ICreateUpdateRoleRequest {
    roleId: string | undefined;
    name: string | undefined;

    constructor(data?: Partial<ICreateUpdateRoleRequest>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.roleId = _data["roleId"];
            this.name = _data["name"];
        }
    }

    static fromJS(data: any): CreateUpdateRoleRequest {
        data = typeof data === 'object' ? data : {};
        let result = new CreateUpdateRoleRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["roleId"] = this.roleId;
        data["name"] = this.name;
        return data;
    }

    clone(): CreateUpdateRoleRequest {
        const json = this.toJSON();
        let result = new CreateUpdateRoleRequest();
        result.init(json);
        return result;
    }
}

export interface ICreateUpdateRoleRequest {
    roleId: string | undefined;
    name: string | undefined;
}

export class CreateUpdateRoleResult implements ICreateUpdateRoleResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    roleId: string;
    created: boolean;

    constructor(data?: Partial<ICreateUpdateRoleResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (this as any).isError = _data["isError"];
            (this as any).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : undefined as any;
            (this as any).errorMessage = _data["errorMessage"];
            this.roleId = _data["roleId"];
            this.created = _data["created"];
        }
    }

    static fromJS(data: any): CreateUpdateRoleResult {
        data = typeof data === 'object' ? data : {};
        let result = new CreateUpdateRoleResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        data["errorMessage"] = this.errorMessage;
        data["roleId"] = this.roleId;
        data["created"] = this.created;
        return data;
    }

    clone(): CreateUpdateRoleResult {
        const json = this.toJSON();
        let result = new CreateUpdateRoleResult();
        result.init(json);
        return result;
    }
}

export interface ICreateUpdateRoleResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    roleId: string;
    created: boolean;
}

export class CreateUpdateUserRequest implements ICreateUpdateUserRequest {
    username: string | undefined;
    email: string | undefined;
    firstName: string | undefined;
    lastName: string | undefined;
    password: string | undefined;
    roles: string[] | undefined;

    constructor(data?: Partial<ICreateUpdateUserRequest>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.username = _data["username"];
            this.email = _data["email"];
            this.firstName = _data["firstName"];
            this.lastName = _data["lastName"];
            this.password = _data["password"];
            if (Array.isArray(_data["roles"])) {
                this.roles = [] as any;
                for (let item of _data["roles"])
                    this.roles!.push(item);
            }
        }
    }

    static fromJS(data: any): CreateUpdateUserRequest {
        data = typeof data === 'object' ? data : {};
        let result = new CreateUpdateUserRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["username"] = this.username;
        data["email"] = this.email;
        data["firstName"] = this.firstName;
        data["lastName"] = this.lastName;
        data["password"] = this.password;
        if (Array.isArray(this.roles)) {
            data["roles"] = [];
            for (let item of this.roles)
                data["roles"].push(item);
        }
        return data;
    }

    clone(): CreateUpdateUserRequest {
        const json = this.toJSON();
        let result = new CreateUpdateUserRequest();
        result.init(json);
        return result;
    }
}

export interface ICreateUpdateUserRequest {
    username: string | undefined;
    email: string | undefined;
    firstName: string | undefined;
    lastName: string | undefined;
    password: string | undefined;
    roles: string[] | undefined;
}

export class CreateUpdateUserResult implements ICreateUpdateUserResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    userId: string;
    created: boolean;

    constructor(data?: Partial<ICreateUpdateUserResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (this as any).isError = _data["isError"];
            (this as any).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : undefined as any;
            (this as any).errorMessage = _data["errorMessage"];
            this.userId = _data["userId"];
            this.created = _data["created"];
        }
    }

    static fromJS(data: any): CreateUpdateUserResult {
        data = typeof data === 'object' ? data : {};
        let result = new CreateUpdateUserResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        data["errorMessage"] = this.errorMessage;
        data["userId"] = this.userId;
        data["created"] = this.created;
        return data;
    }

    clone(): CreateUpdateUserResult {
        const json = this.toJSON();
        let result = new CreateUpdateUserResult();
        result.init(json);
        return result;
    }
}

export interface ICreateUpdateUserResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    userId: string;
    created: boolean;
}

export class DataFilter implements IDataFilter {
    filterType: FilterType;
    dataType: FilterDataType;
    memberName: string | undefined;
    values: string[] | undefined;
    period: PeriodType;
    fromDate: Date;
    toDate: Date;
    fromValue: string | undefined;
    toValue: string | undefined;

    constructor(data?: Partial<IDataFilter>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.filterType = _data["filterType"];
            this.dataType = _data["dataType"];
            this.memberName = _data["memberName"];
            if (Array.isArray(_data["values"])) {
                this.values = [] as any;
                for (let item of _data["values"])
                    this.values!.push(item);
            }
            this.period = _data["period"];
            this.fromDate = _data["fromDate"] ? new Date(_data["fromDate"].toString()) : undefined as any;
            this.toDate = _data["toDate"] ? new Date(_data["toDate"].toString()) : undefined as any;
            this.fromValue = _data["fromValue"];
            this.toValue = _data["toValue"];
        }
    }

    static fromJS(data: any): DataFilter {
        data = typeof data === 'object' ? data : {};
        let result = new DataFilter();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["filterType"] = this.filterType;
        data["dataType"] = this.dataType;
        data["memberName"] = this.memberName;
        if (Array.isArray(this.values)) {
            data["values"] = [];
            for (let item of this.values)
                data["values"].push(item);
        }
        data["period"] = this.period;
        data["fromDate"] = this.fromDate ? this.fromDate.toISOString() : undefined as any;
        data["toDate"] = this.toDate ? this.toDate.toISOString() : undefined as any;
        data["fromValue"] = this.fromValue;
        data["toValue"] = this.toValue;
        return data;
    }

    clone(): DataFilter {
        const json = this.toJSON();
        let result = new DataFilter();
        result.init(json);
        return result;
    }
}

export interface IDataFilter {
    filterType: FilterType;
    dataType: FilterDataType;
    memberName: string | undefined;
    values: string[] | undefined;
    period: PeriodType;
    fromDate: Date;
    toDate: Date;
    fromValue: string | undefined;
    toValue: string | undefined;
}

export class DiagnosticsResponse implements IDiagnosticsResponse {
    scheme: string | undefined;
    xfp: string | undefined;
    xfHost: string | undefined;
    openIddictIssuer: string | undefined;
    appOptions: AppConfigOptions;

    constructor(data?: Partial<IDiagnosticsResponse>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.scheme = _data["scheme"];
            this.xfp = _data["xfp"];
            this.xfHost = _data["xfHost"];
            this.openIddictIssuer = _data["openIddictIssuer"];
            this.appOptions = _data["appOptions"] ? AppConfigOptions.fromJS(_data["appOptions"]) : undefined as any;
        }
    }

    static fromJS(data: any): DiagnosticsResponse {
        data = typeof data === 'object' ? data : {};
        let result = new DiagnosticsResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["scheme"] = this.scheme;
        data["xfp"] = this.xfp;
        data["xfHost"] = this.xfHost;
        data["openIddictIssuer"] = this.openIddictIssuer;
        data["appOptions"] = this.appOptions ? this.appOptions.toJSON() : undefined as any;
        return data;
    }

    clone(): DiagnosticsResponse {
        const json = this.toJSON();
        let result = new DiagnosticsResponse();
        result.init(json);
        return result;
    }
}

export interface IDiagnosticsResponse {
    scheme: string | undefined;
    xfp: string | undefined;
    xfHost: string | undefined;
    openIddictIssuer: string | undefined;
    appOptions: AppConfigOptions;
}

export class ErrorDto implements IErrorDto {
    code: string | undefined;
    description: string | undefined;
    type: ErrorType;

    constructor(data?: Partial<IErrorDto>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.description = _data["description"];
            this.type = _data["type"];
        }
    }

    static fromJS(data: any): ErrorDto {
        data = typeof data === 'object' ? data : {};
        let result = new ErrorDto();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["description"] = this.description;
        data["type"] = this.type;
        return data;
    }

    clone(): ErrorDto {
        const json = this.toJSON();
        let result = new ErrorDto();
        result.init(json);
        return result;
    }
}

export interface IErrorDto {
    code: string | undefined;
    description: string | undefined;
    type: ErrorType;
}

export enum ErrorType {
    None = "None",
    Failure = "Failure",
    Unexpected = "Unexpected",
    Validation = "Validation",
    Conflict = "Conflict",
    NotFound = "NotFound",
    Unauthorized = "Unauthorized",
    Forbidden = "Forbidden",
    Custom = "Custom",
}

export class ExpressEntityInfo implements IExpressEntityInfo {
    id: string | undefined;
    name: string | undefined;
    type: string | undefined;

    constructor(data?: Partial<IExpressEntityInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.type = _data["type"];
        }
    }

    static fromJS(data: any): ExpressEntityInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ExpressEntityInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["type"] = this.type;
        return data;
    }

    clone(): ExpressEntityInfo {
        const json = this.toJSON();
        let result = new ExpressEntityInfo();
        result.init(json);
        return result;
    }
}

export interface IExpressEntityInfo {
    id: string | undefined;
    name: string | undefined;
    type: string | undefined;
}

export enum FilterDataType {
    String = "String",
    Number = "Number",
    Guid = "Guid",
    DateTime = "DateTime",
}

export class FilterGroup implements IFilterGroup {
    type: FilterType;
    dataType: FilterDataType;
    displayName: string | undefined;
    memberName: string | undefined;
    options: FilterOption[] | undefined;
    index: number;

    constructor(data?: Partial<IFilterGroup>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.type = _data["type"];
            this.dataType = _data["dataType"];
            this.displayName = _data["displayName"];
            this.memberName = _data["memberName"];
            if (Array.isArray(_data["options"])) {
                this.options = [] as any;
                for (let item of _data["options"])
                    this.options!.push(FilterOption.fromJS(item));
            }
            this.index = _data["index"];
        }
    }

    static fromJS(data: any): FilterGroup {
        data = typeof data === 'object' ? data : {};
        let result = new FilterGroup();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["type"] = this.type;
        data["dataType"] = this.dataType;
        data["displayName"] = this.displayName;
        data["memberName"] = this.memberName;
        if (Array.isArray(this.options)) {
            data["options"] = [];
            for (let item of this.options)
                data["options"].push(item ? item.toJSON() : undefined as any);
        }
        data["index"] = this.index;
        return data;
    }

    clone(): FilterGroup {
        const json = this.toJSON();
        let result = new FilterGroup();
        result.init(json);
        return result;
    }
}

export interface IFilterGroup {
    type: FilterType;
    dataType: FilterDataType;
    displayName: string | undefined;
    memberName: string | undefined;
    options: FilterOption[] | undefined;
    index: number;
}

export class FilterOption implements IFilterOption {
    value: string | undefined;
    displayName: string | undefined;
    parentId: string | undefined;
    path: string | undefined;

    constructor(data?: Partial<IFilterOption>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.value = _data["value"];
            this.displayName = _data["displayName"];
            this.parentId = _data["parentId"];
            this.path = _data["path"];
        }
    }

    static fromJS(data: any): FilterOption {
        data = typeof data === 'object' ? data : {};
        let result = new FilterOption();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["value"] = this.value;
        data["displayName"] = this.displayName;
        data["parentId"] = this.parentId;
        data["path"] = this.path;
        return data;
    }

    clone(): FilterOption {
        const json = this.toJSON();
        let result = new FilterOption();
        result.init(json);
        return result;
    }
}

export interface IFilterOption {
    value: string | undefined;
    displayName: string | undefined;
    parentId: string | undefined;
    path: string | undefined;
}

export class FilterResult implements IFilterResult {
    filters: FilterGroup[] | undefined;

    constructor(data?: Partial<IFilterResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["filters"])) {
                (this as any).filters = [] as any;
                for (let item of _data["filters"])
                    (this as any).filters!.push(FilterGroup.fromJS(item));
            }
        }
    }

    static fromJS(data: any): FilterResult {
        data = typeof data === 'object' ? data : {};
        let result = new FilterResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.filters)) {
            data["filters"] = [];
            for (let item of this.filters)
                data["filters"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): FilterResult {
        const json = this.toJSON();
        let result = new FilterResult();
        result.init(json);
        return result;
    }
}

export interface IFilterResult {
    filters: FilterGroup[] | undefined;
}

export enum FilterType {
    Options = "Options",
    Period = "Period",
    Range = "Range",
    Tree = "Tree",
}

export class FormDefinition implements IFormDefinition {
    name: string | undefined;
    fields: FormFieldDefinition[] | undefined;

    constructor(data?: Partial<IFormDefinition>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"];
            if (Array.isArray(_data["fields"])) {
                (this as any).fields = [] as any;
                for (let item of _data["fields"])
                    (this as any).fields!.push(FormFieldDefinition.fromJS(item));
            }
        }
    }

    static fromJS(data: any): FormDefinition {
        data = typeof data === 'object' ? data : {};
        let result = new FormDefinition();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name;
        if (Array.isArray(this.fields)) {
            data["fields"] = [];
            for (let item of this.fields)
                data["fields"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): FormDefinition {
        const json = this.toJSON();
        let result = new FormDefinition();
        result.init(json);
        return result;
    }
}

export interface IFormDefinition {
    name: string | undefined;
    fields: FormFieldDefinition[] | undefined;
}

export class FormFieldDefinition implements IFormFieldDefinition {
    name: string | undefined;
    fieldType: FormFieldType;
    isRequired: boolean;
    row: number | undefined;
    column: number | undefined;
    span: number | undefined;
    label: string | undefined;
    options: OptionItem[] | undefined;

    constructor(data?: Partial<IFormFieldDefinition>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"];
            this.fieldType = _data["fieldType"];
            this.isRequired = _data["isRequired"];
            this.row = _data["row"];
            this.column = _data["column"];
            this.span = _data["span"];
            this.label = _data["label"];
            if (Array.isArray(_data["options"])) {
                this.options = [] as any;
                for (let item of _data["options"])
                    this.options!.push(OptionItem.fromJS(item));
            }
        }
    }

    static fromJS(data: any): FormFieldDefinition {
        data = typeof data === 'object' ? data : {};
        let result = new FormFieldDefinition();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name;
        data["fieldType"] = this.fieldType;
        data["isRequired"] = this.isRequired;
        data["row"] = this.row;
        data["column"] = this.column;
        data["span"] = this.span;
        data["label"] = this.label;
        if (Array.isArray(this.options)) {
            data["options"] = [];
            for (let item of this.options)
                data["options"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): FormFieldDefinition {
        const json = this.toJSON();
        let result = new FormFieldDefinition();
        result.init(json);
        return result;
    }
}

export interface IFormFieldDefinition {
    name: string | undefined;
    fieldType: FormFieldType;
    isRequired: boolean;
    row: number | undefined;
    column: number | undefined;
    span: number | undefined;
    label: string | undefined;
    options: OptionItem[] | undefined;
}

export enum FormFieldType {
    Text = "Text",
    Number = "Number",
    Date = "Date",
    Time = "Time",
    DateTime = "DateTime",
    Checkbox = "Checkbox",
    Radio = "Radio",
    Select = "Select",
    TextArea = "TextArea",
}

export class GetLookupsResult implements IGetLookupsResult {
    items: LookupItem[] | undefined;

    constructor(data?: Partial<IGetLookupsResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(LookupItem.fromJS(item));
            }
        }
    }

    static fromJS(data: any): GetLookupsResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetLookupsResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): GetLookupsResult {
        const json = this.toJSON();
        let result = new GetLookupsResult();
        result.init(json);
        return result;
    }
}

export interface IGetLookupsResult {
    items: LookupItem[] | undefined;
}

export class GetRolesResult implements IGetRolesResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    roles: string[] | undefined;

    constructor(data?: Partial<IGetRolesResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (this as any).isError = _data["isError"];
            (this as any).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : undefined as any;
            (this as any).errorMessage = _data["errorMessage"];
            if (Array.isArray(_data["roles"])) {
                this.roles = [] as any;
                for (let item of _data["roles"])
                    this.roles!.push(item);
            }
        }
    }

    static fromJS(data: any): GetRolesResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetRolesResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        data["errorMessage"] = this.errorMessage;
        if (Array.isArray(this.roles)) {
            data["roles"] = [];
            for (let item of this.roles)
                data["roles"].push(item);
        }
        return data;
    }

    clone(): GetRolesResult {
        const json = this.toJSON();
        let result = new GetRolesResult();
        result.init(json);
        return result;
    }
}

export interface IGetRolesResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    roles: string[] | undefined;
}

export class ListOutputOfAppRoleInfo implements IListOutputOfAppRoleInfo {
    items: AppRoleInfo[] | undefined;
    totalCount: number;

    constructor(data?: Partial<IListOutputOfAppRoleInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(AppRoleInfo.fromJS(item));
            }
            this.totalCount = _data["totalCount"];
        }
    }

    static fromJS(data: any): ListOutputOfAppRoleInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ListOutputOfAppRoleInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : undefined as any);
        }
        data["totalCount"] = this.totalCount;
        return data;
    }

    clone(): ListOutputOfAppRoleInfo {
        const json = this.toJSON();
        let result = new ListOutputOfAppRoleInfo();
        result.init(json);
        return result;
    }
}

export interface IListOutputOfAppRoleInfo {
    items: AppRoleInfo[] | undefined;
    totalCount: number;
}

export class ListOutputOfAppUserInfo implements IListOutputOfAppUserInfo {
    items: AppUserInfo[] | undefined;
    totalCount: number;

    constructor(data?: Partial<IListOutputOfAppUserInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(AppUserInfo.fromJS(item));
            }
            this.totalCount = _data["totalCount"];
        }
    }

    static fromJS(data: any): ListOutputOfAppUserInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ListOutputOfAppUserInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : undefined as any);
        }
        data["totalCount"] = this.totalCount;
        return data;
    }

    clone(): ListOutputOfAppUserInfo {
        const json = this.toJSON();
        let result = new ListOutputOfAppUserInfo();
        result.init(json);
        return result;
    }
}

export interface IListOutputOfAppUserInfo {
    items: AppUserInfo[] | undefined;
    totalCount: number;
}

export class ListOutputOfStandardItem implements IListOutputOfStandardItem {
    items: StandardItem[] | undefined;
    totalCount: number;

    constructor(data?: Partial<IListOutputOfStandardItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["items"])) {
                this.items = [] as any;
                for (let item of _data["items"])
                    this.items!.push(StandardItem.fromJS(item));
            }
            this.totalCount = _data["totalCount"];
        }
    }

    static fromJS(data: any): ListOutputOfStandardItem {
        data = typeof data === 'object' ? data : {};
        let result = new ListOutputOfStandardItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.items)) {
            data["items"] = [];
            for (let item of this.items)
                data["items"].push(item ? item.toJSON() : undefined as any);
        }
        data["totalCount"] = this.totalCount;
        return data;
    }

    clone(): ListOutputOfStandardItem {
        const json = this.toJSON();
        let result = new ListOutputOfStandardItem();
        result.init(json);
        return result;
    }
}

export interface IListOutputOfStandardItem {
    items: StandardItem[] | undefined;
    totalCount: number;
}

export class ListRequestOfAppRoleInfo implements IListRequestOfAppRoleInfo {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;

    constructor(data?: Partial<IListRequestOfAppRoleInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.skip = _data["skip"];
            this.take = _data["take"];
            this.sortBy = _data["sortBy"];
            this.sortDescending = _data["sortDescending"];
            (this as any).sortDirection = _data["sortDirection"];
            this.relatedId = _data["relatedId"];
            this.searchText = _data["searchText"];
            if (Array.isArray(_data["filters"])) {
                this.filters = [] as any;
                for (let item of _data["filters"])
                    this.filters!.push(DataFilter.fromJS(item));
            }
            this.memberName = _data["memberName"];
            this.value = _data["value"];
        }
    }

    static fromJS(data: any): ListRequestOfAppRoleInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ListRequestOfAppRoleInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["skip"] = this.skip;
        data["take"] = this.take;
        data["sortBy"] = this.sortBy;
        data["sortDescending"] = this.sortDescending;
        data["sortDirection"] = this.sortDirection;
        data["relatedId"] = this.relatedId;
        data["searchText"] = this.searchText;
        if (Array.isArray(this.filters)) {
            data["filters"] = [];
            for (let item of this.filters)
                data["filters"].push(item ? item.toJSON() : undefined as any);
        }
        data["memberName"] = this.memberName;
        data["value"] = this.value;
        return data;
    }

    clone(): ListRequestOfAppRoleInfo {
        const json = this.toJSON();
        let result = new ListRequestOfAppRoleInfo();
        result.init(json);
        return result;
    }
}

export interface IListRequestOfAppRoleInfo {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;
}

export class ListRequestOfAppUserInfo implements IListRequestOfAppUserInfo {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;

    constructor(data?: Partial<IListRequestOfAppUserInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.skip = _data["skip"];
            this.take = _data["take"];
            this.sortBy = _data["sortBy"];
            this.sortDescending = _data["sortDescending"];
            (this as any).sortDirection = _data["sortDirection"];
            this.relatedId = _data["relatedId"];
            this.searchText = _data["searchText"];
            if (Array.isArray(_data["filters"])) {
                this.filters = [] as any;
                for (let item of _data["filters"])
                    this.filters!.push(DataFilter.fromJS(item));
            }
            this.memberName = _data["memberName"];
            this.value = _data["value"];
        }
    }

    static fromJS(data: any): ListRequestOfAppUserInfo {
        data = typeof data === 'object' ? data : {};
        let result = new ListRequestOfAppUserInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["skip"] = this.skip;
        data["take"] = this.take;
        data["sortBy"] = this.sortBy;
        data["sortDescending"] = this.sortDescending;
        data["sortDirection"] = this.sortDirection;
        data["relatedId"] = this.relatedId;
        data["searchText"] = this.searchText;
        if (Array.isArray(this.filters)) {
            data["filters"] = [];
            for (let item of this.filters)
                data["filters"].push(item ? item.toJSON() : undefined as any);
        }
        data["memberName"] = this.memberName;
        data["value"] = this.value;
        return data;
    }

    clone(): ListRequestOfAppUserInfo {
        const json = this.toJSON();
        let result = new ListRequestOfAppUserInfo();
        result.init(json);
        return result;
    }
}

export interface IListRequestOfAppUserInfo {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;
}

export class ListRequestOfStandardItem implements IListRequestOfStandardItem {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;

    constructor(data?: Partial<IListRequestOfStandardItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.skip = _data["skip"];
            this.take = _data["take"];
            this.sortBy = _data["sortBy"];
            this.sortDescending = _data["sortDescending"];
            (this as any).sortDirection = _data["sortDirection"];
            this.relatedId = _data["relatedId"];
            this.searchText = _data["searchText"];
            if (Array.isArray(_data["filters"])) {
                this.filters = [] as any;
                for (let item of _data["filters"])
                    this.filters!.push(DataFilter.fromJS(item));
            }
            this.memberName = _data["memberName"];
            this.value = _data["value"];
        }
    }

    static fromJS(data: any): ListRequestOfStandardItem {
        data = typeof data === 'object' ? data : {};
        let result = new ListRequestOfStandardItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["skip"] = this.skip;
        data["take"] = this.take;
        data["sortBy"] = this.sortBy;
        data["sortDescending"] = this.sortDescending;
        data["sortDirection"] = this.sortDirection;
        data["relatedId"] = this.relatedId;
        data["searchText"] = this.searchText;
        if (Array.isArray(this.filters)) {
            data["filters"] = [];
            for (let item of this.filters)
                data["filters"].push(item ? item.toJSON() : undefined as any);
        }
        data["memberName"] = this.memberName;
        data["value"] = this.value;
        return data;
    }

    clone(): ListRequestOfStandardItem {
        const json = this.toJSON();
        let result = new ListRequestOfStandardItem();
        result.init(json);
        return result;
    }
}

export interface IListRequestOfStandardItem {
    skip: number;
    take: number;
    sortBy: string | undefined;
    sortDescending: boolean;
    sortDirection: string | undefined;
    relatedId: string | undefined;
    searchText: string | undefined;
    filters: DataFilter[] | undefined;
    memberName: string | undefined;
    value: string | undefined;
}

export class LookupItem implements ILookupItem {
    value: string | undefined;
    label: string | undefined;

    constructor(data?: Partial<ILookupItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.value = _data["value"];
            this.label = _data["label"];
        }
    }

    static fromJS(data: any): LookupItem {
        data = typeof data === 'object' ? data : {};
        let result = new LookupItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["value"] = this.value;
        data["label"] = this.label;
        return data;
    }

    clone(): LookupItem {
        const json = this.toJSON();
        let result = new LookupItem();
        result.init(json);
        return result;
    }
}

export interface ILookupItem {
    value: string | undefined;
    label: string | undefined;
}

export class OptionItem implements IOptionItem {
    id: string;
    group: string | undefined;
    value: string | undefined;
    display: string | undefined;

    constructor(data?: Partial<IOptionItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.group = _data["group"];
            this.value = _data["value"];
            this.display = _data["display"];
        }
    }

    static fromJS(data: any): OptionItem {
        data = typeof data === 'object' ? data : {};
        let result = new OptionItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["group"] = this.group;
        data["value"] = this.value;
        data["display"] = this.display;
        return data;
    }

    clone(): OptionItem {
        const json = this.toJSON();
        let result = new OptionItem();
        result.init(json);
        return result;
    }
}

export interface IOptionItem {
    id: string;
    group: string | undefined;
    value: string | undefined;
    display: string | undefined;
}

export enum PeriodType {
    All = "All",
    Today = "Today",
    ThisWeek = "ThisWeek",
    ThisMonth = "ThisMonth",
    Custom = "Custom",
}

export class RemoteServiceErrorInfo implements IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;

    constructor(data?: Partial<IRemoteServiceErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.code = _data["code"];
            this.message = _data["message"];
            this.details = _data["details"];
            if (_data["data"]) {
                this.data = {} as any;
                for (let key in _data["data"]) {
                    if (_data["data"].hasOwnProperty(key))
                        (this.data as any)![key] = _data["data"][key];
                }
            }
            if (Array.isArray(_data["validationErrors"])) {
                this.validationErrors = [] as any;
                for (let item of _data["validationErrors"])
                    this.validationErrors!.push(RemoteServiceValidationErrorInfo.fromJS(item));
            }
        }
    }

    static fromJS(data: any): RemoteServiceErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["code"] = this.code;
        data["message"] = this.message;
        data["details"] = this.details;
        if (this.data) {
            data["data"] = {};
            for (let key in this.data) {
                if (this.data.hasOwnProperty(key))
                    (data["data"] as any)[key] = (this.data as any)[key];
            }
        }
        if (Array.isArray(this.validationErrors)) {
            data["validationErrors"] = [];
            for (let item of this.validationErrors)
                data["validationErrors"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): RemoteServiceErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorInfo {
    code: string | undefined;
    message: string | undefined;
    details: string | undefined;
    data: { [key: string]: any; } | undefined;
    validationErrors: RemoteServiceValidationErrorInfo[] | undefined;
}

export class RemoteServiceErrorResponse implements IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;

    constructor(data?: Partial<IRemoteServiceErrorResponse>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.error = _data["error"] ? RemoteServiceErrorInfo.fromJS(_data["error"]) : undefined as any;
        }
    }

    static fromJS(data: any): RemoteServiceErrorResponse {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceErrorResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        return data;
    }

    clone(): RemoteServiceErrorResponse {
        const json = this.toJSON();
        let result = new RemoteServiceErrorResponse();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceErrorResponse {
    error: RemoteServiceErrorInfo;
}

export class RemoteServiceValidationErrorInfo implements IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;

    constructor(data?: Partial<IRemoteServiceValidationErrorInfo>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.message = _data["message"];
            if (Array.isArray(_data["members"])) {
                this.members = [] as any;
                for (let item of _data["members"])
                    this.members!.push(item);
            }
        }
    }

    static fromJS(data: any): RemoteServiceValidationErrorInfo {
        data = typeof data === 'object' ? data : {};
        let result = new RemoteServiceValidationErrorInfo();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["message"] = this.message;
        if (Array.isArray(this.members)) {
            data["members"] = [];
            for (let item of this.members)
                data["members"].push(item);
        }
        return data;
    }

    clone(): RemoteServiceValidationErrorInfo {
        const json = this.toJSON();
        let result = new RemoteServiceValidationErrorInfo();
        result.init(json);
        return result;
    }
}

export interface IRemoteServiceValidationErrorInfo {
    message: string | undefined;
    members: string[] | undefined;
}

export class StandardItem implements IStandardItem {
    id: string | undefined;
    name: string | undefined;

    constructor(data?: Partial<IStandardItem>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
        }
    }

    static fromJS(data: any): StandardItem {
        data = typeof data === 'object' ? data : {};
        let result = new StandardItem();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        return data;
    }

    clone(): StandardItem {
        const json = this.toJSON();
        let result = new StandardItem();
        result.init(json);
        return result;
    }
}

export interface IStandardItem {
    id: string | undefined;
    name: string | undefined;
}

export class UpdatePasswordRequest implements IUpdatePasswordRequest {
    userId: string;
    password: string | undefined;

    constructor(data?: Partial<IUpdatePasswordRequest>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userId = _data["userId"];
            this.password = _data["password"];
        }
    }

    static fromJS(data: any): UpdatePasswordRequest {
        data = typeof data === 'object' ? data : {};
        let result = new UpdatePasswordRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["userId"] = this.userId;
        data["password"] = this.password;
        return data;
    }

    clone(): UpdatePasswordRequest {
        const json = this.toJSON();
        let result = new UpdatePasswordRequest();
        result.init(json);
        return result;
    }
}

export interface IUpdatePasswordRequest {
    userId: string;
    password: string | undefined;
}

export class UpdatePasswordResult implements IUpdatePasswordResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    isUpdated: boolean;

    constructor(data?: Partial<IUpdatePasswordResult>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            (this as any).isError = _data["isError"];
            (this as any).isSuccess = _data["isSuccess"];
            this.error = _data["error"] ? ErrorDto.fromJS(_data["error"]) : undefined as any;
            (this as any).errorMessage = _data["errorMessage"];
            this.isUpdated = _data["isUpdated"];
        }
    }

    static fromJS(data: any): UpdatePasswordResult {
        data = typeof data === 'object' ? data : {};
        let result = new UpdatePasswordResult();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["isSuccess"] = this.isSuccess;
        data["error"] = this.error ? this.error.toJSON() : undefined as any;
        data["errorMessage"] = this.errorMessage;
        data["isUpdated"] = this.isUpdated;
        return data;
    }

    clone(): UpdatePasswordResult {
        const json = this.toJSON();
        let result = new UpdatePasswordResult();
        result.init(json);
        return result;
    }
}

export interface IUpdatePasswordResult {
    isError: boolean;
    isSuccess: boolean;
    error: ErrorDto;
    errorMessage: string | undefined;
    isUpdated: boolean;
}

export class ValueOptionsGroup implements IValueOptionsGroup {
    name: string | undefined;
    values: OptionItem[] | undefined;

    constructor(data?: Partial<IValueOptionsGroup>) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (this as any)[property] = (data as any)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.name = _data["name"];
            if (Array.isArray(_data["values"])) {
                this.values = [] as any;
                for (let item of _data["values"])
                    this.values!.push(OptionItem.fromJS(item));
            }
        }
    }

    static fromJS(data: any): ValueOptionsGroup {
        data = typeof data === 'object' ? data : {};
        let result = new ValueOptionsGroup();
        result.init(data);
        return result;
    }

    toJSON(data?: Partial<any>) {
        data = typeof data === 'object' ? data : {};
        data["name"] = this.name;
        if (Array.isArray(this.values)) {
            data["values"] = [];
            for (let item of this.values)
                data["values"].push(item ? item.toJSON() : undefined as any);
        }
        return data;
    }

    clone(): ValueOptionsGroup {
        const json = this.toJSON();
        let result = new ValueOptionsGroup();
        result.init(json);
        return result;
    }
}

export interface IValueOptionsGroup {
    name: string | undefined;
    values: OptionItem[] | undefined;
}

export class ApiException extends Error {
    message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}