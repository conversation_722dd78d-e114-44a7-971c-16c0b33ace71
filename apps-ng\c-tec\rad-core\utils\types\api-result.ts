import { Observable, firstValueFrom, timeInterval, timeout } from "rxjs";

export interface IApiOutput {
  hasErrors: boolean;
  isSuccessful: boolean;
  friendlyErrorMessage: string | undefined;
}


export interface ICanError {
  isError: boolean;
  ['key']: any

}

export interface IRequestResult<T = undefined>{
  readonly value: T | undefined;
  readonly isSuccess: boolean;
  readonly error: string | undefined;
  get isError(): boolean;
}

export class RequestResult<T = undefined> implements IRequestResult<T> {
  _value: T|undefined;
  
  get value(): T|undefined {
    return this._value;
  }

  isSuccess: boolean;
  
  _error: string|undefined;

  get error(){
    return this.error;
  }

  get isError() {
    return !this.isSuccess;
  }

  constructor(result?: T, error: string|undefined = undefined) {
    if (error) {
      this.isSuccess = false;
      this._error = error;
     
    } else {
      this._value = result;
    }
  }



  static create<T = undefined>(value: T|IApiOutput) {


      let hasErrors = false;
      let friendlyErrorMessage: string|undefined = undefined;
      if(value["hasErrors"]) {
        hasErrors = value["hasErrors"];
      }

      if(value["friendlyErrorMessage"]) {
        friendlyErrorMessage = value["friendlyErrorMessage"];
      }

      if(hasErrors) {

        if(!friendlyErrorMessage || friendlyErrorMessage.length == 0){
          friendlyErrorMessage = "An error occured";
        }

       
        return new ApiResult<T>(undefined, friendlyErrorMessage);

      }else{
        return new ApiResult<T>(value as T);
      }
    
  }



  static success<T>(value: T = undefined) {
    return ApiResult.create(value);
  }

  static cancelled() {
    return new RequestResult(undefined, "The operation was cancelled");
  }

  static error(error = "An error occured") {
    return new ApiResult(undefined, error);
  }
}




export interface IApiResult<T> extends IRequestResult<T> {
  readonly value: T | undefined;

}

export class ApiResult<T> implements IApiResult<T> {
    _value: T|undefined;
    
    get value(): T|undefined {
      return this._value;
    }

   
    
    _error: string|undefined;

    get error(){
      return this._error;
    }

    get isError() {
      return this._error !== undefined;
    }

    get isSuccess() {
      return !this.isError;
    }

  

    constructor(result?: T, error: string|undefined = undefined) {
      if (error) {
        this._error = error;
       
      } else {
        this._value = result;
        
      }
    }


  
    static create<T = unknown>(value: T|IApiOutput) {

        if(value === null || value === undefined) {
          return new ApiResult<T>(null);
        }


        let hasErrors = false;
        let friendlyErrorMessage: string|undefined = undefined;
        if(value["isError"]) {
          hasErrors = value["isError"];
        }

        if(value["errorMessage"]) {
          friendlyErrorMessage = value["errorMessage"];
        }

        if(hasErrors) {

          if(!friendlyErrorMessage || friendlyErrorMessage.length == 0){
            friendlyErrorMessage = "An error occured";
          }

         
          return new ApiResult<T>(undefined, friendlyErrorMessage);

        }else{
          return new ApiResult<T>(value as T);
        }
      
    }

    static success(value = undefined) {
      return ApiResult.create(value);
    }

   

    static error(error = "An error occured") {
      return new ApiResult(undefined, error);
    }
  }

  export async  function apiResultFrom<T>(source: Observable<T>, timeoutInMs: number = undefined): Promise<ApiResult<T>> {


    try {
      let result: Awaited<T> = null;

      if(timeoutInMs) {
        result = await firstValueFrom(source.pipe(timeout(timeoutInMs)));
      } else {
        result = await firstValueFrom(source);
      }

      
      return ApiResult.create(result);
    } catch (error) {
      console.log(error);
      return new ApiResult<T>(undefined, error.message? error.message: "An error occured");
      
    }

   
  }

  export async  function executeApi<T>(source: Observable<T>, errorMsg = "An error occured", timeoutInMs: number = undefined): Promise<T> {


    try {
      let result: Awaited<T> = null;

      if(timeoutInMs) {
        result = await firstValueFrom(source.pipe(timeout(timeoutInMs)));
      } else {
        result = await firstValueFrom(source);
      }

      
      const apiResult = ApiResult.create(result);

      if(apiResult.isError) {
        throw new Error(apiResult.error);
      }
      return apiResult.value as T;

    } catch (error) {
      console.log(error);
      throw new Error(errorMsg);
      
      
    }

   
  }