import { computed, inject, Inject, Injectable, InjectionToken, signal } from '@angular/core';

import { AppNavigationService, INavItem, NavigationItem } from '@tec/rad-core/abstractions';




import { Observable, of } from 'rxjs';
import { APP_MENU_ITEMS, IAppMenuService } from '@tec/rad-app/core';
import { configureNavItem } from '@tec/rad-ui/navbar';




@Injectable({
    providedIn: 'root'
  })
  export class RadAppMenuService implements IAppMenuService { 


    private appMenuItems: INavItem[] = inject(APP_MENU_ITEMS, {optional: true}) || []
    private routeService: AppNavigationService = inject(AppNavigationService)
    private _menuItems = signal<INavItem[]>([]);
    constructor(
    ) {
    

      if(this.appMenuItems.length > 0) {  
        
        this.loadMenu(this.appMenuItems)
      }
        
    
    }

    loadMenu(items: INavItem[]) {
      const menuItems = []
      for(const item of items) {
        this.configureItem(item);
        menuItems.push(item);
      }

      this._menuItems.set(menuItems)

    }


    private configureItem(item: INavItem) {
      if(item.page) {
        item.link = this.routeService.getUrl(item.page)
        if(!item.link) {
          item.link = `view/${item.page}` 
        }

      }

      if(item.children && item.children.length > 0) {
        for(const child of item.children) {
          this.configureItem(child)
        }
      }

    }

    menuItems = computed(() => { 
     const items=  configureNavItem(this._menuItems());
     return items;
    }
    )
  }