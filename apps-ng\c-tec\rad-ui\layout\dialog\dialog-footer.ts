import { CommonModule } from '@angular/common';
import { Component, computed, inject, resource, signal, input, EventEmitter, Output } from '@angular/core';
import { ColorContext } from '@tec/rad-core/abstractions';
import { SeverityPipe } from '@tec/rad-ui/common';
import { RadButtonModule } from '@tec/rad-ui/component';

@Component({
  selector: 'rad-dialog-footer',
  imports: [CommonModule, RadButtonModule, SeverityPipe],
  styles: [],
  template: `
    <div class="w-full flex-0 border-t border-t-gray-200">
      <div class="flex flex-row sm:flex-row sm:items-center justify-end gap-4 p-4">
        <!-- Cancel -->
        <p-button severity="secondary" size="small" [label]="cancelLabel()" (click)="onCancel()"> </p-button>
        <!-- Send -->
        <p-button [severity]="severity() | pSeverity" size="small" [label]="confirmLabel()" (click)="onConfirm()"> </p-button>
      </div>
    </div>
  `,
})
export class RadDialogFooter {
  confirmLabel = input('Ok');

  cancelLabel = input('Cancel');

  severity = input<ColorContext>('primary');

  @Output() readonly confirm: EventEmitter<any> = new EventEmitter<any>();

  @Output() readonly cancel: EventEmitter<any> = new EventEmitter<any>();

  onCancel() {
    this.cancel.next({});
  }

  onConfirm() {
    this.confirm.next({});
  }
}
