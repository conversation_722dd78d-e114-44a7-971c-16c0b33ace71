import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Subject } from 'rxjs';

@Component({
    selector: 'rad-shell-empty-layout',
    templateUrl: './empty.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        RouterModule
    ]
})
export class EmptyLayoutComponent implements OnDestroy
{
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor()
    {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On destroy
     */
    ngOnDestroy(): void
    {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
}
