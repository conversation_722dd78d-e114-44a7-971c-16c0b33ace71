import { inject, Injectable } from '@angular/core';

import { ConfirmationConfig, AppConfirmationService, AppDialogService } from '@tec/rad-core/abstractions';
import { ConfirmationDialog } from './confirmation.dialog';
import { DialogResult, DialogSize } from '@tec/rad-core/abstractions';

import { IDialogResult } from '@tec/rad-core/abstractions';

@Injectable({providedIn: 'root'})
export class RadConfirmationService extends AppConfirmationService
{

    private dialog = inject(AppDialogService);
    private defaultConfirmation: ConfirmationConfig = {
        title: "Confirm",
        message: "Are you sure?",
        dismissible: false,

        showIcon: true,
        icon: 'ri-error-warning-line',
        
        showCancel: false,
        cancelLabel: "Cancel",

        showConfirm: true,
        confirmLabel: "Ok",
        severity: 'primary',



    }

    async showConfirmation(config: ConfirmationConfig = {}): Promise<IDialogResult>
    {
        // Merge the user config with the default config
        const userConfig = {...this.defaultConfirmation, ...config};

        // Open the dialog
        const result  = await this.dialog.show(ConfirmationDialog,userConfig, {size: 'medium', });

        if(result.completed) {
            return DialogResult.complete("completed")
        }else
        {
            return DialogResult.cancel<string>();
        }


    }

    /** CONFIRMATION SERVICE */
    confirm(message: string, title: string = "Confirm"): Promise<IDialogResult>{

        return this.showConfirmation({
            message: message,
            title: title,
            showCancel: true
        });


    }

   warn(message:string, title?:string): Promise<IDialogResult> {
        return this.showConfirmation({
            message: message,
            title: title? title: "Warning",
            dismissible: false,
            icon: 'ri-alert-line',
            severity: 'warning',
            showCancel: true,

        })

    }

    success(message:string, title?:string): Promise<IDialogResult>{
        return  this.showConfirmation({
            message: message,
            title: title? title: "Success",
            showCancel: false,
            severity: 'success',
            icon: 'ri-check-line',
        })
    }

    error(message:string, title?:string): Promise<IDialogResult> {
        return this.showConfirmation({
            message: message,
            title: title? title: "Error occured",
            showCancel: false,
            severity: 'danger',
            icon: 'ri-error-line',
    });
}






}
