//resolve the course id from the route and load the course data
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { CourseFacade } from '../core';
import { inject } from '@angular/core';
import { injectRouteParam } from '@tec/rad-core/utils';


  
  export const courseResolverFn = (
    route: ActivatedRouteSnapshot,
    
  ) => {
    const courseFacade = inject(CourseFacade)
    const courseId = route.params['courseId'];
    return courseFacade.getCourseStructure(courseId);
  };