import { Component, Inject, InjectionToken, OnD<PERSON>roy, OnInit, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';
import { combineLatest, filter, map, Subject, takeUntil } from 'rxjs';

//import { FUSE_VERSION } from '@tec/rad-ui';
import { RegionManager, ContentRegion, RadCoreMediaWatcherService } from '@tec/rad-core/composition';
import { ExpressAppConfig } from '../config/config.types';
import { EmptyLayoutComponent } from './empty/empty.component';
import {  RadUiConfigService } from '@tec/rad-core/composition';



const SHELL_REGION = new InjectionToken<RegionManager>("SHELL_REGION")
@Component({
    selector: 'rad-core-shell',
    templateUrl: './shell.component.html',
    styleUrls: ['./shell.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        EmptyLayoutComponent,
        RouterModule,
        ContentRegion
    ],
    providers: [
        { provide: SHELL_REGION, useClass: RegionManager }
    ]
})
export class RadShellComponent implements OnInit, OnDestroy
{
    config: ExpressAppConfig;
    layout: string;
    scheme: 'dark' | 'light';
    theme: string;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    @ViewChild(ContentRegion, {  static: true })
    public regionContainer: ContentRegion;
    /**
     * Constructor
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        @Inject(DOCUMENT) private _document: any,
        private _renderer2: Renderer2,
        private _router: Router,
        private _radConfigService: RadUiConfigService,
        private _radMediaWatcherService: RadCoreMediaWatcherService,
        @Inject(SHELL_REGION) protected shellRegion: RegionManager
    )
    {
        const s =1;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void
    {
        this.shellRegion.registerRegion(this.regionContainer)
        // Set the theme and scheme based on the configuration
        combineLatest([
            this._radConfigService.config$,
            this._radMediaWatcherService.onMediaQueryChange$(['(prefers-color-scheme: dark)', '(prefers-color-scheme: light)'])
        ]).pipe(
            takeUntil(this._unsubscribeAll),
            map(([config, mql]) => {

                const options = {
                    scheme: config.scheme,
                    theme : config.theme
                };

                // If the scheme is set to 'auto'...
                if ( config.scheme === 'auto' )
                {
                    // Decide the scheme using the media query
                    options.scheme = mql.breakpoints['(prefers-color-scheme: dark)'] ? 'dark' : 'light';
                }

                return options;
            })
        ).subscribe((options) => {

            // Store the options
            this.scheme = options.scheme;
            this.theme = options.theme;

            // Update the scheme and theme
            this._updateScheme();
            this._updateTheme();
        });

        // Subscribe to config changes
        this._radConfigService.config$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((config: ExpressAppConfig) => {

                // Store the config
                this.config = config;

                // Update the layout
                this._updateLayout();
            });

        // Subscribe to NavigationEnd event
        this._router.events.pipe(
            filter(event => event instanceof NavigationEnd),
            takeUntil(this._unsubscribeAll)
        ).subscribe(() => {

            // Update the layout
            this._updateLayout();
        });

        // Set the app version
        //this._renderer2.setAttribute(this._document.querySelector('[ng-version]'), 'rad-version', FUSE_VERSION);
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void
    {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Private methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Update the selected layout
     */
    private _updateLayout(): void
    {
        // Get the current activated route
        let route = this._activatedRoute;
        while ( route.firstChild )
        {
            route = route.firstChild;
        }

        // 1. Set the layout from the config
        if(this.layout != this.config.layout){
        this.layout = this.config.layout;
        }

        // 2. Get the query parameter from the current route and
        // set the layout and save the layout to the config
        const layoutFromQueryParam = (route.snapshot.queryParamMap.get('layout') as string);
        if ( layoutFromQueryParam )
        {
            this.layout = layoutFromQueryParam;
            if ( this.config )
            {
                this.config.layout = layoutFromQueryParam;
            }
        }

        // 3. Iterate through the paths and change the layout as we find
        // a config for it.
        //
        // The reason we do this is that there might be empty grouping
        // paths or componentless routes along the path. Because of that,
        // we cannot just assume that the layout configuration will be
        // in the last path's config or in the first path's config.
        //
        // So, we get all the paths that matched starting from root all
        // the way to the current activated route, walk through them one
        // by one and change the layout as we find the layout config. This
        // way, layout configuration can live anywhere within the path and
        // we won't miss it.
        //
        // Also, this will allow overriding the layout in any time so we
        // can have different layouts for different routes.
        const paths = route.pathFromRoot;
        paths.forEach((path) => {

            // Check if there is a 'layout' data
            if ( path.routeConfig && path.routeConfig.data && path.routeConfig.data.layout )
            {
                // Set the layout
                this.layout = path.routeConfig.data.layout;
            }
        });
        if(this.layout !== 'empty') {
            this.shellRegion.showView(this.layout)
        }else {
            this.shellRegion.showView("EmptyLayout")
        }
    }

    /**
     * Update the selected scheme
     *
     * @private
     */
    private _updateScheme(): void
    {
        // Remove class names for all schemes
        this._document.body.classList.remove('light', 'dark');

        // Add class name for the currently selected scheme
        this._document.body.classList.add(this.scheme);
    }

    /**
     * Update the selected theme
     *
     * @private
     */
    private _updateTheme(): void
    {
        // Find the class name for the previously selected theme and remove it
        this._document.body.classList.forEach((className: string) => {
            if ( className.startsWith('theme-') )
            {
                this._document.body.classList.remove(className, className.split('-')[1]);
            }
        });

        // Add class name for the currently selected theme
        this._document.body.classList.add(this.theme);
    }


}
