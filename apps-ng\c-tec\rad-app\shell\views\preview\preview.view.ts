import { Component, OnInit, signal, ViewChild, ViewContainerRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute } from '@angular/router';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RegionManager, ContentRegion } from '@tec/rad-core/composition'
import { AppNavigationService } from '@tec/rad-core/abstractions';


export interface MyQueryParams {
  id: any;
  [key: string]: any; // You can type specific fields if needed
}
@Component({
  selector: 'rad-shell-preview-view',
  templateUrl: './preview.view.html',
  styles: [":host { height: 100%; width: 100%; display: flex;}"],
  imports: [CommonModule,
    MatSidenavModule,
    MatButtonModule,
    MatIconModule,
    ContentRegion
  ],
  providers: [RegionManager]
})
export class PreviewView implements OnInit {


  @ViewChild(ContentRegion, { static: true })
  public region: ContentRegion

  size: string = injectRouteParam("size")

  drawerMode: 'over' | 'side' = 'side';
  drawerOpened = true;
  widthClass  = signal('640px');


  constructor(
    private route: ActivatedRoute,
    private regionManager: RegionManager,
    private navService: AppNavigationService

  ) {
  }



  async ngOnInit() {

    this.regionManager.registerRegion(this.region);
    this.route.params.subscribe(async (param) => {

      const viewName = param.view;
      const id = param.id;

      const path = this.navService.getPath(viewName);

      if (path) {
        await this.navService.goTo(path.name, id);
        return;
      }


      const params = this.getQueryParamsAsObject(this.route);

      const widthParam = params['w'];
    if (widthParam) {
      this.widthClass.set(`${widthParam}px`);
    }

      await this.regionManager.showView(viewName, params)


    }



    );
  }

  getQueryParamsAsObject(route: ActivatedRoute): MyQueryParams {
    const params = route.snapshot.queryParams;
    const result: MyQueryParams = {id: undefined};
  
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        result[key] = params[key];
      }
    }
  
    return result;
  }

}
