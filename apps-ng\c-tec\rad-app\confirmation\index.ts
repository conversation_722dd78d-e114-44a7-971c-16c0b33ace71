
import {EnvironmentProviders,  Provider} from "@angular/core";
import { AppConfirmationService } from "@tec/rad-core/abstractions";
import { RadConfirmationService as RadAppConfirmationService }from "./confirmation.service";




//export * from './ngx-log.service';

//provide logging service
export function provideRadAppConfirmation(): Array<Provider | EnvironmentProviders> {
  return [
    {
      provide: AppConfirmationService,
      useClass: RadAppConfirmationService,
    }
  ];
}
