import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { INavItem, injectTabReference } from '@tec/rad-core/abstractions';
import { injectRouteParam } from '@tec/rad-core/utils';
import { RadContentLayout, RadHeader, RadRouterOutlet, RadSideMaster, RadSideNav } from '@tec/rad-ui/layout';
import { RadSidebar } from '@tec/rad-ui/navbar';



@Component({
  selector: 'ed-course-run',
 
 
  imports: [
    CommonModule,
    RadSideNav,
    RadSidebar,
    RouterModule,
    RadHeader,
    RadContentLayout,
    //Add required imports here
    RadRouterOutlet
  ],
   template: `
  <rad-side-nav [divider]="true" drawerWidth="180" >
   
      
      
  
        <rad-sidebar radSideNavDrawer side class="h-full overflow-clip bg-slate-50"
          [appearance]="'default'"
          [navigation]="nav"
          [inner]="true"
          [mode]="'side'"
          name="course-sidebar-navigation"
          [opened]="true"
        >
    <rad-header radSidebarHeader radContentHeader title="Grade 5 Pep" [divider]="true">
  
      </rad-header>
    </rad-sidebar>
  

  
    <rad-router-outlet></rad-router-outlet>
    <!-- <div class="flex flex-col flex-auto animation-2">
      <router-outlet *ngIf="true"></router-outlet>
    </div>
     -->
  
  
  </rad-side-nav>
  `,
})
export class CourseRunPage {


  #id = injectRouteParam('courseId');
  #tabRef = injectTabReference();

  protected nav: INavItem[] = [
    {
      title: 'Details',
      icon: 'ri-information-line',
      link: 'view/CourseRunDetail',
    },
    {
      title: 'Schedule',
      icon: 'ri-calendar-line',
      link: 'view/CourseRunSchedule',
    },






  ];




}