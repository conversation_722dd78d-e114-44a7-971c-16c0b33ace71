import { inject, Injectable, resource } from "@angular/core";
import { Course, CourseApiProxy } from "@ed/shared/data-academics";
import { injectTabReference, IServiceState } from "@tec/rad-core/abstractions";
import { RadSignalBase } from "@tec/rad-core/services";
import { executeApi, injectRouteParam } from "@tec/rad-core/utils";


interface CourseState extends IServiceState {
    course: Course;
}

const initialState: CourseState = {
    course: undefined
}

@Injectable()
export class CourseController extends RadSignalBase<CourseState> {

    #courseApi = inject(CourseApiProxy);
    private _courseId = injectRouteParam("courseId");
    private _tab = injectTabReference();

    // private _course = resource({
    //     params: ()=> {
    //         return {
    //         courseId: this._courseId
    //         }
    //     },
    //     loader: (r) =>{
    //         const courseId = r.params.courseId;
    //         if(!courseId) {
    //             return undefined;
    //         }
    //         return executeApi(this.#courseApi.getCourse(courseId))
    //     } 
    // })  


    course = this.select(n => n.course());


    constructor() {
        super(initialState);
        this.initialize().then();
    }

    async initialize(): Promise<void> {
        await this.execute$(this.#courseApi.getCourse(this._courseId),{
            success: (r) => {

                this.patchState({course: r.course});
                this._tab?.setTitle("Course", r.course.name);
            }
        }
    )
    
        
    }

}