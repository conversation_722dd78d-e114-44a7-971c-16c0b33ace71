import { inject, Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import { AppConfigurationService, AppNavigationService } from '@tec/rad-core/abstractions';










@Injectable()
export class StartupGuard  {

    #nav = inject(AppNavigationService)
    #config  = inject(AppConfigurationService)
    
    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
        const home = this.#config.getConfigSection('application.home') as string;

       
        if(home) {
            this.goToHome(home);
            return false;
        }

        
        return true;
    }

    canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
        return this.canActivate(route, state);
    }



    private goToHome(homePage:string){
        // Redirect to the sign-in page
        this.#nav.goTo(homePage);


    }
}
