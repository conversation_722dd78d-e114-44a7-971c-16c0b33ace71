import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FormlyModule } from '@ngx-formly/core';
import { SelectModule } from 'primeng/select';
import { FormlySelectModule as FormlyCoreSelectModule } from '@ngx-formly/core/select';
import { FormlyFormFieldModule } from '@ngx-formly/primeng/form-field';
import { RadFormlySelect } from './select.type';
import { RadFormlyListbox } from './listbox.type';
import { FluidModule } from 'primeng/fluid';

@NgModule({
  //declarations: [RadFormlySelect],
  imports: [
    RadFormlySelect,
    CommonModule,
    ReactiveFormsModule,
    SelectModule,
    FluidModule,
    FormlyFormFieldModule,
    FormlyCoreSelectModule,
    FormlyModule.forChild({
      types: [
        {
          name: 'select',
          component: RadFormlySelect,
          wrappers: ['rad-form-field'],
        },
        { name: 'enum', extends: 'select' },
        { name: 'listbox',
          component: RadFormlyListbox,
          wrappers: ['rad-form-field'],



        },
      ],
    }),
  ],
})
export class RadFormlySelectModule {}
