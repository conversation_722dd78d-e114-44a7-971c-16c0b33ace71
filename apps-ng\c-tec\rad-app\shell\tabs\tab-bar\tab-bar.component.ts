import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

import {Router} from "@angular/router";


import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';


import { MatTabsModule } from '@angular/material/tabs';
import { TabItem } from '../tab-item';
import { TabManagerService } from '../tab-manager.service';
import { TooltipModule } from 'primeng/tooltip';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';




@Component({
    selector: 'rad-shell-tab-bar',
    templateUrl: './tab-bar.component.html',
    styleUrls: ['./tab-bar.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        DragDropModule,
        MatTabsModule,
        TooltipModule
    ]
})
export class ShellTabBarComponent implements OnInit {
  @Output() activeTabChange: EventEmitter<TabItem> = new EventEmitter();
  @Output() tabClose: EventEmitter<TabItem> = new EventEmitter();


  tabs: TabItem[];

  constructor(private router: Router,
              private tabService: TabManagerService,
              private changeDetector: ChangeDetectorRef
              ) {
                this.tabService.tabs$.pipe(
        takeUntilDestroyed()
    ).subscribe(tabs=>{
        this.tabs = tabs;
        this.changeDetector.markForCheck();
    })
  }

  ngOnInit() {
    
  }




  async tabClicked(tab: TabItem): Promise<void> {
    console.log(`Tab clicked with url ${tab.currentUrl}`)
    const defTab = this.tabs.find(n=>n.isDefault);
    this.tabService.selectTab(tab);

  }

  closeTab(tab: TabItem): void {
    this.tabService.closeTab(tab);
  }

  drop(event: CdkDragDrop<TabItem[]>) {
    moveItemInArray(this.tabs, event.previousIndex, event.currentIndex);
  }

  look($event: MouseEvent) {
    console.log($event);
  }

  onTabClicked(event: MouseEvent, tab: TabItem) {
    if (event.button === 1) {
      this.closeTab(tab);
    }
  }


}


