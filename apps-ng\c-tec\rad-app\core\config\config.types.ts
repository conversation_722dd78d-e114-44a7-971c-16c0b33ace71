import { InjectionToken } from "@angular/core";

// Types
export type Scheme = 'auto' | 'dark' | 'light';
export type Screens = { [key: string]: string };
export type Theme = 'theme-default' | string;
export type Themes = { id: string; name: string }[];

/**
 * AppConfig interface. Update this interface to strictly type your config
 * object.
 */
export interface RadUiConfig
{
    layout?: string;
    scheme?: Scheme;
    screens?: Screens;
    theme?: Theme;
    themes?: Themes;
}


export const RAD_UI_CONFIG = new InjectionToken<RadUiConfig>('RAD_UI_CONFIG');