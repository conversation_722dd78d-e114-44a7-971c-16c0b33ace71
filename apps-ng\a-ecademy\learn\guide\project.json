{"name": "ed-pep-guide", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "a-ecademy/learn/guide/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "a-ecademy/learn/guide/ng-package.json", "tsConfig": "a-ecademy/learn/guide/tsconfig.lib.json"}, "configurations": {"production": {"tsConfig": "a-ecademy/learn/guide/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "lint": {"executor": "@nx/eslint:lint"}}}